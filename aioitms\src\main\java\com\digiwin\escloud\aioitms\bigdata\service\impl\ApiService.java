package com.digiwin.escloud.aioitms.bigdata.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.SaveParams;
import com.digiwin.escloud.aioitms.bigdata.dao.ApiMapper;
import com.digiwin.escloud.aioitms.bigdata.model.*;
import com.digiwin.escloud.aioitms.bigdata.service.IApiService;
import com.digiwin.escloud.aioitms.bigdata.utils.SqlUtils;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aioitms.report.dao.AiopsDeviceTpMapper;
import com.digiwin.escloud.aioitms.report.model.AiopsDeviceTp;
import com.digiwin.escloud.aioitms.report.model.serviceReport.AsiaInfoVulnerabilityEsReport;
import com.digiwin.escloud.aioitms.report.service.AiopsUserService;
import com.digiwin.escloud.aioitms.report.service.serviceReprot.AsiaInfoVulnerabilityEsReportService;
import com.digiwin.escloud.aioitms.util.RestUtil;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.model.JdbcSqlInfo;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;

import java.time.LocalDateTime;

import com.digiwin.escloud.etl.model.EtlModelField;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 2022-11-18 16:22
 * @Description
 */
@Slf4j
@Service
public class ApiService implements IApiService {

    @Resource
    private ApiMapper apiMapper;
    @Resource
    private RestUtil restUtil;
    @Autowired
    private BigDataUtil bigDataUtil;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private AiopsUserService aiopsUserService;

    @Resource
    private AsiaInfoVulnerabilityEsReportService asiaInfoVulnerabilityEsReportService;
    @Resource
    private AiopsDeviceTpMapper aiopsDeviceTpMapper;

    @Override
    public Map<String, Object> dataQuery(String apiCode, int pageNum, int pageSize, ApiParam apiParam) {
        AioApi aioApi = apiMapper.getAioApi(apiCode);
        String jdbcSql = aioApi.getJdbcSql();
        boolean needPage = aioApi.isNeedPage();
        int apiType = aioApi.getApiType();
        String sql = null;
        if (ApiType.SQL.getIndex() == apiType) {
            sql = SqlUtils.buildJdbcSqlBySqlParam(jdbcSql, apiParam);
        } else {
            //todo 配置库、表、列
        }
        Map<String, Object> map = new HashMap<>();
        map.put("total", 0);
        if (needPage && sql != null) { //20241118: 代码稽核 StringUtils.isNotBlank(sql)改为sql != null
            int fromIdx = sql.indexOf("from ");
            int orderIdx = sql.indexOf("order by ");
            String fromSql = sql.substring(fromIdx, orderIdx);
            String countSql = "select count(*) as \"total\" " + fromSql;
            List<Map<String, Object>> countDataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(aioApi.getDbEngine(), countSql));
            if (CollectionUtils.isEmpty(countDataList)) {
                map.put("list", new ArrayList<>());
                map.put("total", 0);
                return map;
            }
            map = countDataList.get(0);
            Integer total = (Integer) map.get("total");
            if (total != null && total == 0) {
                map.put("list", new ArrayList<>());
                return map;
            }
            sql = sql + " limit " + pageSize + " offset " + (pageNum - 1) * pageSize;
        }
        List<Map<String, Object>> dataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(aioApi.getDbEngine(), sql));
        map.put("list", dataList);
        return map;
    }

    @Override
    public void dataMapping(List<Map<String, Object>> sourceData, String sinkType, String sinkName, String schemaName) {

//        BaseResponse response = aioCmdbFeignClient.getEtlSinkFields(sinkType, schemaName, sinkName);
        BaseResponse response = restUtil.getEtlSinkFields(sinkType, schemaName, sinkName);
        List<Map<String, Object>> data = (List<Map<String, Object>>) response.getData();
        Map<String, List<Map<String, Object>>> dataMap = data.stream()
                .filter(i -> Objects.nonNull(i.get("fieldTypeEnums")))
                .collect(Collectors.toMap(
                        i -> i.get("fieldCode").toString(),
                        k -> (List<Map<String, Object>>) k.get("fieldTypeEnums"),
                        (existingValue, newValue) -> existingValue
                ));

        Set<String> dataMapKeys = dataMap.keySet();

        sourceData.forEach(map -> {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                if (dataMapKeys.contains(key)) {
                    List<Map<String, Object>> fieldTypeEnums = dataMap.get(key);
                    Map<String, String> enumsMap = fieldTypeEnums.stream().collect(Collectors.toMap(i -> i.get("enumCode").toString(), k -> k.get("enumName").toString()));
                    map.put(key, enumsMap.get(entry.getValue().toString()));
                }
            }
        });

    }

    @Override
    public ResponseBase saveData(SaveParams saveParams) {
        BaseResponse response = restUtil.getEtlSinkFields(saveParams.getSinkType(), saveParams.getSchemaName(), saveParams.getTableName());
        if (!response.checkIsSuccess() || Objects.isNull(response.getData())) {
            return ResponseBase.error("1", "model etl not found");
        }
        List<Map<String, Object>> fieldData = (List<Map<String, Object>>) response.getData();
        List<EtlModelField> etlModelFieldList = buildListFromMapList(fieldData, EtlModelField.class);
        if (CollectionUtils.isEmpty(etlModelFieldList)) {
            return ResponseBase.error("1", "etl field is empty");
        }
        String insertSql = buildInsertSqlV2(saveParams, etlModelFieldList);
        Integer i = bigDataUtil.srSave(insertSql);
        if (i == 0) {
            return ResponseBase.error("1", "data insert fail");
        }
        return ResponseBase.ok();

    }

    @Override
    public BaseResponse saveDataStream(SaveParams saveParams) {
        BaseResponse response = restUtil.getEtlSinkFields(saveParams.getSinkType(), saveParams.getSchemaName(), saveParams.getTableName());
        if (!response.checkIsSuccess() || Objects.isNull(response.getData())) {
            return BaseResponse.error("1", "model etl not found");
        }
        List<Map<String, Object>> fieldData = (List<Map<String, Object>>) response.getData();
        List<EtlModelField> etlModelFieldList = buildListFromMapList(fieldData, EtlModelField.class);
        if (CollectionUtils.isEmpty(etlModelFieldList)) {
            return BaseResponse.error("1", "etl field is empty");
        }
        StarRocksEntity starRocksEntity = buildStarRocksEntity(saveParams, etlModelFieldList);
        try {
            Map<String, Object> map = bigDataUtil.srStreamLoadThrowsException(starRocksEntity);
            BaseResponse baseResponse = ConvertUtil.convertToObject(map, BaseResponse.class);
            return baseResponse;
        } catch (Exception e) {
            log.error("[saveDataStream] error : {}", e.getMessage(), e);
            return BaseResponse.error("1", e.getMessage());
        }

    }

    @Override
    public ResponseBase deleteOracleLinuxDiskioData(Long eid, String deviceId, String collectedTime) {
        return ResponseBase.ok(bigDataUtil.srSave("delete from servicecloud.OracleLinuxDiskio where eid = " + eid + " and deviceId = '" + deviceId + "'" + " and collectedTime = '" + collectedTime + "'"));
    }

    @Override
    public ResponseBase getAsiaVulnInfo(String deviceId, String startTime, String endTime, Long eid) {
        AiopsDeviceTp queryParam = new AiopsDeviceTp();
        queryParam.setEid(eid);
        queryParam.setDeviceId(deviceId);
        List<AiopsDeviceTp> aiopsDeviceTps = aiopsDeviceTpMapper.selectAiopsDeviceTpList(queryParam);
        Map<String, Object> resMap = new HashMap<>();
        if (CollectionUtils.isEmpty(aiopsDeviceTps)) {
            Map<String, Object> map = buildCveVulnData(deviceId, startTime, endTime, eid);
            return ResponseBase.ok(map);
        }
        AiopsDeviceTp aiopsDeviceTp = aiopsDeviceTps.get(0);
        List<Map<String, String>> asiaDeviceIdList = new ArrayList<>();
        Map<String, String> conditionMap1 = new HashMap<>();
        conditionMap1.put("key", "last_scan_time");
        conditionMap1.put("op", ">=");
        conditionMap1.put("value", startTime);

        Map<String, String> conditionMap2 = new HashMap<>();
        conditionMap2.put("key", "last_scan_time");
        conditionMap2.put("op", "<");
        conditionMap2.put("value", endTime);
        asiaDeviceIdList.add(conditionMap1);
        asiaDeviceIdList.add(conditionMap2);
        List<AsiaInfoVuln> vulnInfoList = asiaInfoVulnerabilityEsReportService.getVulnInfo(null, asiaDeviceIdList, true
                , Stream.of(aiopsDeviceTp.getTpDeviceId()).collect(Collectors.toList()));
        // 因为查询逻辑一样 所以使用 亚信漏洞报告对象存储 该设备漏洞和 设备漏洞特征数

        //漏洞详情
        if (CollectionUtils.isEmpty(vulnInfoList)) {
            Map<String, Object> map = buildCveVulnData(deviceId, startTime, endTime, eid);
            return ResponseBase.ok(map);
        }
        resMap.put("vulnInfo", vulnInfoList);

        // 漏洞特征
        List<AsiaInfoVulnerabilityEsReport.VulnTagDetail> vulnTagDetailList = asiaInfoVulnerabilityEsReportService.buildVulnTagDetailList(vulnInfoList);
        String maxCollectedTime = vulnTagDetailList.stream()
                .map(AsiaInfoVulnerabilityEsReport.VulnTagDetail::getCollectedTime)
                .max(Comparator.naturalOrder())
                .orElse(null);
        for (AsiaInfoVulnerabilityEsReport.VulnTagDetail vulnTagDetail : vulnTagDetailList) {
            vulnTagDetail.setCollectedTime(maxCollectedTime);
        }
        // 漏洞级别
        List<AsiaInfoVulnerabilityEsReport.VulnSeverityDetail> vulnSeverityDetailList =
                asiaInfoVulnerabilityEsReportService.buildAsiaVulnSeverityList(vulnInfoList);

        resMap.put("vulnTagDetailList", vulnTagDetailList);
        resMap.put("vulnSeverityDetailList", vulnSeverityDetailList);
        return ResponseBase.ok(resMap);
    }

    private Map<String, Object> buildCveVulnData(String deviceId, String startDateStr, String endDateStr, Long eid) {
        Map<String, Object> resMap = new HashMap<>();
        List<Map<String, Object>> cveVulnDataMapList = getCveVulnData(deviceId, startDateStr, endDateStr, eid);
        resMap.put("vulnInfo", cveVulnDataMapList);
        
        // 计算cveVulnDataMapList中最大的collectedTime
        String maxCollectedTime = cveVulnDataMapList.stream()
                .map(item -> String.valueOf(item.get("collectedTime")))
                .max(Comparator.naturalOrder())
                .orElse(null);
        
        List<Map<String, Object>> vulnTagDetailList = cveVulnDataMapList.stream()
                .collect(Collectors.groupingBy(i -> Objects.toString(i.get("vuln_tag"))))
                .entrySet().stream().map(entry -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("vulnTag", entry.getKey());
                    List<Map<String, Object>> valueList = entry.getValue();
                    result.put("collectedTime", maxCollectedTime);
                    //此处不应该加asia 方便统一获取数据
                    result.put("asiaInfoVulnList", valueList);
                    // 添加最大的collectedTime到结果中
                    return result;
                }).collect(Collectors.toList());
        resMap.put("vulnTagDetailList", vulnTagDetailList);
        // 将最大的collectedTime也添加到返回结果中

        // 4 为严重级别
        Map<String, Object> map = new HashMap<>();
        map.put("severity", 4);
        map.put("asiaInfoVulnList", cveVulnDataMapList);
        List<Map<String, Object>> vulnSeverityDetailList = Lists.newArrayList(map);
        resMap.put("vulnSeverityDetailList", vulnSeverityDetailList);
        return resMap;
    }

    private List<Map<String, Object>> getCveVulnData(String deviceId, String startDateStr, String endDateStr, Long eid) {
        String dbName = "servicecloud";

        String cveQuerySql = "select vuln_tag, code, 'CVE' as wType,collectedTime as collectedTime from (\n" +
                "   select '远程利用' as vuln_tag, code,collectedTime from %s.CVE20200796EventModel \n" +
                "   where eid = '%s' \n" +
                "     and deviceId = '%s'\n" +
                "     and collectedTime = (select max(collectedTime) from %s.CVE20200796EventModel \n" +
                "                          where eid = '%s' \n" +
                "                          and deviceId = '%s'\n" +
                "                          and collectedTime between '%s' and '%s') \n" +
                "   union all\n" +
                "   select '远程利用' as vuln_tag,code,collectedTime from %s.MS17010EventModel \n" +
                "   where eid = '%s' \n" +
                "     and deviceId = '%s'\n" +
                "     and collectedTime = (select max(collectedTime) from %s.MS17010EventModel \n" +
                "                          where eid = '%s' \n" +
                "                          and deviceId = '%s' \n" +
                "                          and collectedTime between '%s' and '%s')\n" +
                "   union all\n" +
                "   select '本地提权' as vuln_tag,code,collectedTime from %s.CVE20201472EventModel \n" +
                "   where eid = '%s' \n" +
                "     and deviceId = '%s'\n" +
                "     and collectedTime = (select max(collectedTime) from %s.CVE20201472EventModel \n" +
                "                          where eid = '%s' \n" +
                "                          and deviceId = '%s' \n" +
                "                          and collectedTime between '%s' and '%s')\n" +
                ") a  ";

        cveQuerySql = String.format(cveQuerySql,
                dbName, eid, deviceId, dbName, eid, deviceId, startDateStr, endDateStr,
                dbName, eid, deviceId, dbName, eid, deviceId, startDateStr, endDateStr,
                dbName, eid, deviceId, dbName, eid, deviceId, startDateStr, endDateStr
        );

        return bigDataUtil.srQuery(cveQuerySql);
    }

    @Override
    public void queryReBuild(Query query) {
        log.info("[queryReBuild] query : {}", query);
        if (!BooleanUtil.objectToBoolean(query.getNeedAuth())) {
            return;
        }
        List<Long> eidList = aiopsUserService.getEidList();
        if (CollectionUtils.isEmpty(eidList)) {
            return;
        }
        Query.BusinessCondition bc = new Query.BusinessCondition("eid", "VARCHAR", "in",
                eidList.stream().map(Object::toString).collect(Collectors.joining(",")));

        if (Objects.isNull(query.getBusinessCondition())) {
            query.setBusinessCondition(new ArrayList<>());
        }

        query.getBusinessCondition().add(bc);
    }

    private void buildQuery4ForAIOSSM(Query query) {
        List<Long> eidList = aiopsUserService.getAuthEidList();
        if (CollectionUtils.isEmpty(eidList)) {
            return;
        }
        Query.BusinessCondition bc = new Query.BusinessCondition("eid", "VARCHAR", "in",
                eidList.stream().map(Object::toString).collect(Collectors.joining(",")));
        if (Objects.isNull(query.getBusinessCondition())) {
            query.setBusinessCondition(new ArrayList<>());
        }
        query.getBusinessCondition().add(bc);
    }

    @Override
    public void queryBuildByAuth(Query query) {
        if (!BooleanUtil.objectToBoolean(query.getNeedAuth())) {
            return;
        }
        String headerPlatformType = RequestUtil.getHeaderPlatformType();
        AioPlatformEnum aioPlatformEnum = EnumUtil.fromString(AioPlatformEnum.class, headerPlatformType, AioPlatformEnum.AIO_MIS_SSM);
        switch (aioPlatformEnum) {
            case AIOSSM:
                buildQuery4ForAIOSSM(query);
            case AIO_MIS_SSM:
            case AIO_MIS_APP:
            default:
        }
    }

    private static <T> List<T> buildListFromMapList(List<Map<String, Object>> result, Class<T> clazz) {
        objectMapper.configure(MapperFeature.USE_ANNOTATIONS, false);

        return result.stream()
                .map(map -> objectMapper.convertValue(map, clazz))
                .collect(Collectors.toList());
    }

    public String buildInsertSql(SaveParams saveParams, Map<String, Pair<String, String>> fieldCodeMap) {

        // 获取dataContent数组
        JSONObject data = saveParams.getData();
        JSONArray dataContents = data.getJSONArray("dataContent");
        JSONObject basicInfo = data.getJSONObject("basicInfo");
        StringBuilder sqlBuilder = new StringBuilder();


        // 遍历dataContent数组，为每个对象生成一条插入语句
        for (int i = 0; i < dataContents.size(); i++) {
            JSONObject dataContent = dataContents.getJSONObject(i);

            sqlBuilder.append("INSERT INTO ")
                    .append(saveParams.getDbName())
                    .append(".")
                    .append(saveParams.getTableName()).append(" ("); // 替换为你的表名

            // 添加列名
            StringBuilder columns = new StringBuilder();
            // 添加值
            StringBuilder values = new StringBuilder();

            // 首先添加外部字段
            for (String key : basicInfo.keySet()) {
                Pair<String, String> stringStringPair = fieldCodeMap.get("BasicInfo." + key);
                String value = stringStringPair.getKey();
                String type = stringStringPair.getValue();
                if (StringUtils.isNotBlank(value)) {
                    columns.append(value).append(", ");
                    values.append(getStringValue(basicInfo, key, type));
                    values.append(", ");
                }

            }

            // 然后添加dataContent中的字段
            for (Map.Entry<String, Pair<String, String>> entry : fieldCodeMap.entrySet()) {
                String key = entry.getKey();
                Pair<String, String> columnTypePair = entry.getValue();
                String column = columnTypePair.getKey();
                String type = columnTypePair.getValue();

                if (key.startsWith("DataContent.")) {
                    key = key.replace("DataContent.", "");
                    if (StringUtils.countMatches(entry.getKey(), ".") > 1) {
                        String[] keyArray = key.split("\\.");
                        JSONObject jo = dataContent;
                        for (int j = 0; j < keyArray.length; j++) {
                            if (j == keyArray.length - 1) {
                                columns.append(column).append(", ");
                                values.append(getStringValue(jo, keyArray[j], type)).append(", ");
                            } else {
                                jo = jo.getJSONObject(keyArray[j]);
                            }
                        }
                    } else {
                        columns.append(column).append(", ");
                        values.append(getStringValue(dataContent, key, type)).append(", ");
                    }
                }
            }

            // 移除最后的逗号和空格
            if (columns.length() > 0) columns.setLength(columns.length() - 2);
            if (values.length() > 0) values.setLength(values.length() - 2);

            sqlBuilder.append(columns).append(") VALUES (").append(values).append(");\n");
        }

        return sqlBuilder.toString();
    }

    public String buildInsertSqlV2(SaveParams saveParams, List<EtlModelField> etlModelFields) {
        if (CollectionUtils.isEmpty(etlModelFields)) {
            return null;
        }

        List<Pair<String, String>> valuePathList = etlModelFields.stream().map(i -> {
                    String valuePath = i.getValuePath();
                    String substring = getSubstring(valuePath);
                    String lowercaseFirstLetter = toLowercaseFirstLetter(substring);
                    return new Pair<>(lowercaseFirstLetter, substring);
                }).filter(i -> !"DataContent".equals(i.getValue()))
                .filter(i -> !"BasicInfo".equals(i.getValue()))
                .collect(Collectors.toList());

        // 获取dataContent数组
        JSONObject data = saveParams.getData();
        JSONArray dataContents = data.getJSONArray("dataContent");
        if (CollectionUtils.isEmpty(dataContents)) {
            dataContents = data.getJSONArray("DataContent");
        }
        JSONObject basicInfo = data.getJSONObject("basicInfo");
        if (basicInfo == null) {
            basicInfo = data.getJSONObject("BasicInfo");
        }

        Configuration conf = Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);
        StringBuilder sqlSb = new StringBuilder();
        sqlSb.append("INSERT INTO ")
                .append(saveParams.getDbName())
                .append(".")
                .append(saveParams.getTableName());
        String insertFieldCodes = etlModelFields.stream().map(o -> o.getFieldCode()).collect(Collectors.joining(","));
        sqlSb.append("(").append(insertFieldCodes).append(")").append(" values ");
        for (int i = 0; i < dataContents.size(); i++) {
            JSONObject modelJson = new JSONObject();
            modelJson.put("BasicInfo", basicInfo);
            modelJson.put("DataContent", dataContents.getJSONObject(i));
            for (Pair<String, String> vp : valuePathList) {
                modelJson.put(vp.getValue(), data.getJSONObject(vp.getValue()));
            }
            StringBuilder valSb = new StringBuilder();
            valSb.append("(");
            for (int j = 0; j < etlModelFields.size(); j++) {
                String fieldCode = etlModelFields.get(j).getFieldCode();
                String fieldType = etlModelFields.get(j).getFieldType();
                String defaultValue = etlModelFields.get(j).getDefaultValue();
                String valuePath = etlModelFields.get(j).getValuePath();
                Object value = JsonPath.using(conf).parse(modelJson).read("$." + valuePath);
                if (value != null && (value instanceof Map || value instanceof List)) {
                    value = JSON.toJSONString(value);
                }
                String insertFieldVal;

                if (value != null) {
                    if ("".equals(value)) {
                        insertFieldVal = "null";
                    } else {
                        insertFieldVal = QueryWrapperHelper.buildVal(fieldType, value.toString());
                    }
                } else {
                    if (!StringUtils.isEmpty(defaultValue)) {
                        insertFieldVal = defaultValue;
                    } else {
                        insertFieldVal = "null";
                    }
                }
                valSb.append(insertFieldVal);
                if (j < etlModelFields.size() - 1) {
                    valSb.append(",");
                }
            }
            valSb.append(")");
            if (i == dataContents.size() - 1) {
                valSb.append(";");
            } else {
                valSb.append(",");
            }
            sqlSb.append(valSb);
        }
        return sqlSb.toString();
    }

    private StarRocksEntity buildStarRocksEntity(SaveParams saveParams, List<EtlModelField> etlModelFields) {
        if (CollectionUtils.isEmpty(etlModelFields)) {
            return null;
        }

        // 1. 初始化 StarRocksEntity
        StarRocksEntity starRocksEntity = new StarRocksEntity();
        starRocksEntity.setTable(saveParams.getTableName());
        starRocksEntity.setDatabase(saveParams.getDbName());
        starRocksEntity.setFieldNames(etlModelFields.stream()
                .map(EtlModelField::getFieldCode)
                .toArray(String[]::new));

        // 2. 获取并处理数据内容
        JSONObject data = saveParams.getData();
        JSONArray dataContents = getDataContents(data);
        JSONObject basicInfo = getBasicInfo(data);

        if (CollectionUtils.isEmpty(dataContents)) {
            return starRocksEntity;
        }

        // 3. 获取额外的值路径
        List<Pair<String, String>> valuePathList = getExtraValuePaths(etlModelFields);

        Configuration conf = Configuration.defaultConfiguration()
                .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL);

        // 5. 构建数据行
        List<LinkedHashMap<String, Object>> rowList = new ArrayList<>();
        for (int i = 0; i < dataContents.size(); i++) {
            JSONObject modelJson = buildModelJson(dataContents.getJSONObject(i), basicInfo, data, valuePathList);
            LinkedHashMap<String, Object> row = buildDataRow(modelJson, etlModelFields, conf);
            rowList.add(row);
        }

        starRocksEntity.setRows(rowList);
        return starRocksEntity;
    }

    private JSONArray getDataContents(JSONObject data) {
        JSONArray dataContents = data.getJSONArray("dataContent");
        return CollectionUtils.isEmpty(dataContents) ? data.getJSONArray("DataContent") : dataContents;
    }

    private JSONObject getBasicInfo(JSONObject data) {
        JSONObject basicInfo = data.getJSONObject("basicInfo");
        return basicInfo == null ? data.getJSONObject("BasicInfo") : basicInfo;
    }

    private List<Pair<String, String>> getExtraValuePaths(List<EtlModelField> etlModelFields) {
        return etlModelFields.stream()
                .map(i -> {
                    String valuePath = i.getValuePath();
                    String substring = getSubstring(valuePath);
                    String lowercaseFirstLetter = toLowercaseFirstLetter(substring);
                    return new Pair<>(lowercaseFirstLetter, substring);
                })
                .filter(i -> !"DataContent".equals(i.getValue()))
                .filter(i -> !"BasicInfo".equals(i.getValue()))
                .collect(Collectors.toList());
    }

    private JSONObject buildModelJson(JSONObject dataContent, JSONObject basicInfo,
                                      JSONObject data, List<Pair<String, String>> valuePathList) {
        JSONObject modelJson = new JSONObject();
        modelJson.put("BasicInfo", basicInfo);
        modelJson.put("DataContent", dataContent);

        for (Pair<String, String> vp : valuePathList) {
            modelJson.put(vp.getValue(), data.getJSONObject(vp.getValue()));
        }
        return modelJson;
    }

    private LinkedHashMap<String, Object> buildDataRow(JSONObject modelJson,
                                                       List<EtlModelField> etlModelFields, Configuration conf) {
        LinkedHashMap<String, Object> row = new LinkedHashMap<>();

        for (EtlModelField field : etlModelFields) {
            String fieldCode = field.getFieldCode();
            Object value = extractFieldValue(modelJson, field, conf);
            row.put(fieldCode, value);
        }

        return row;
    }

    private Object extractFieldValue(JSONObject modelJson, EtlModelField field, Configuration conf) {
        Object value = JsonPath.using(conf).parse(modelJson).read("$." + field.getValuePath());

        if (value == null) {
            return StringUtils.isEmpty(field.getDefaultValue()) ? "" : field.getDefaultValue();
        }

        if (value instanceof Map || value instanceof List) {
            return JSON.toJSONString(value);
        }

        return "".equals(value) ? "" : value;
    }

    private String getStringValue(JSONObject jo, String key, String type) {
        String value = StringUtil.toString(jo.get(key));
        if (Objects.isNull(value)) {
            return null;
        } else {
            return "'" + value + "'";
        }
    }

    public static String getSubstring(String str) {
        int dotIndex = str.indexOf('.');

        if (dotIndex <= 0) {
            return str;
        }

        return str.substring(0, dotIndex);
    }

    private String toLowercaseFirstLetter(String substring) {
        if (substring.length() == 1) {
            return substring.toLowerCase();
        }

        return substring.substring(0, 1).toLowerCase() + substring.substring(1);
    }

    @Override
    public ResponseBase queryDiskDayGrowth(String deviceId) {
        StringBuilder sql = new StringBuilder();
        sql.append("WITH LatestRecordsToday AS (")
                .append("    SELECT disk__path, MAX(collectedTime) AS max_collectedTime")
                .append("    FROM servicecloud.DiskCollected_sr_duplicate")
                .append("    WHERE collectedTime >= CURDATE()")
                .append("      AND collectedTime < CURDATE() + INTERVAL 1 DAY")
                .append("      AND deviceId = '").append(deviceId).append("'")
                .append("    GROUP BY disk__path")
                .append("),")
                .append("LatestRecordsYesterday AS (")
                .append("    SELECT disk__path, MAX(collectedTime) AS max_collectedTime")
                .append("    FROM servicecloud.DiskCollected_sr_duplicate")
                .append("    WHERE collectedTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)")
                .append("      AND collectedTime < CURDATE()")
                .append("      AND deviceId = '").append(deviceId).append("'")
                .append("    GROUP BY disk__path")
                .append(")")
                .append("SELECT")
                .append("    day1.disk__path,")
                .append("    (day2.disk__free - day1.disk__free) AS disk__used_add,")
                .append("    day1.collectedTime AS collectedTime")
                .append(" FROM servicecloud.DiskCollected_sr_duplicate AS day1")
                .append(" INNER JOIN LatestRecordsToday AS lr_today ON day1.disk__path = lr_today.disk__path AND day1.collectedTime = lr_today.max_collectedTime")
                .append(" INNER JOIN (")
                .append("    SELECT *")
                .append("    FROM servicecloud.DiskCollected_sr_duplicate")
                .append("    WHERE collectedTime IN (")
                .append("        SELECT MAX(collectedTime)")
                .append("        FROM servicecloud.DiskCollected_sr_duplicate")
                .append("        WHERE collectedTime >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)")
                .append("          AND collectedTime < CURDATE()")
                .append("          AND deviceId = '").append(deviceId).append("'")
                .append("        GROUP BY disk__path")
                .append("    )")
                .append(") AS day2 ON day1.eid = day2.eid")
                .append("           AND day1.deviceId = day2.deviceId")
                .append("           AND day1.disk__path = day2.disk__path")
                .append("           AND day1.disk__fstype = day2.disk__fstype")
                .append("           AND day1.collectConfigId = day2.collectConfigId")
                .append(" INNER JOIN LatestRecordsYesterday AS lr_yesterday ON day2.disk__path = lr_yesterday.disk__path AND day2.collectedTime = lr_yesterday.max_collectedTime")
                .append(" WHERE day1.deviceId = '").append(deviceId).append("';");

        List<Map<String, Object>> maps = bigDataUtil.srQuery(sql.toString());
        return ResponseBase.ok(maps);
    }

    @Override
    public BaseResponse getDbBackupFailedDetail(String eid, String timeFrom, String timeTo) {
        LocalDateTime now = DateUtil.getLocalNow();
        String from = getTimeFrom(now, timeFrom);
        String to = getTimeTo(now, timeTo);

        CompletableFuture<List<Map<String, Object>>> oracleFuture = CompletableFuture.supplyAsync(
                ()-> getOracleDbBackupFailedDetail(eid, from, to)
        );
        CompletableFuture<List<Map<String, Object>>> sqlServerFuture = CompletableFuture.supplyAsync(
                ()-> getSqlserverDbBackupFailedDetail(eid, from, to)
        );

        // 等待非同步執行結果
        CompletableFuture<List<Map<String, Object>>> combinedFuture = oracleFuture
                .thenCombine(sqlServerFuture, (oracleResult, sqlServerResult) -> {
                    List<Map<String, Object>> combinedList = new ArrayList<>();
                    combinedList.addAll(oracleResult);
                    combinedList.addAll(sqlServerResult);
                    return combinedList;
                })
                .exceptionally(ex -> {
                    log.error("getDbBackupFailedDetail error", ex);
                    return new ArrayList<>();
                });

        List<Map<String, Object>> combinedData = combinedFuture.join();

        // 處理數據
        Map<String, Object> backupFailedData = combinedData.stream()
                .reduce(new HashMap<>(), (acc, entry) -> {
                    acc.put("eid", eid);
                    acc.put("backup_failed_count",
                            StringUtil.toString(LongUtil.objectToLong(acc.get("backup_failed_count"), 0L) + LongUtil.objectToLong(entry.get("backup_failed_count"), 0L)));
                    acc.put("success_count",
                            StringUtil.toString(LongUtil.objectToLong(acc.get("success_count"), 0L) + LongUtil.objectToLong(entry.get("success_count"), 0L)));
                    acc.put("total_success_size",
                            StringUtil.toString( LongUtil.objectToLong(acc.get("total_success_size"), 0L) + LongUtil.objectToLong(entry.get("total_success_size"), 0L)));

                    // 取最大 last_success_time
                    String lastSuccessTime = Objects.nonNull(entry.get("last_success_time")) ? StringUtil.toString(entry.get("last_success_time")) : "";
                    String currentMaxTime = StringUtil.toString(acc.getOrDefault("last_success_time", ""));
                    if (Objects.nonNull(lastSuccessTime) && (Objects.isNull(currentMaxTime) || lastSuccessTime.compareTo(currentMaxTime) > 0)) {
                        acc.put("last_success_time", lastSuccessTime);
                        acc.put("last_success_size", StringUtil.toString(LongUtil.objectToLong(entry.get("last_success_size")))); // 確保對應大小
                    } else {
                        acc.put("last_success_time", currentMaxTime);
                        acc.put("last_success_size", StringUtil.toString(LongUtil.objectToLong(acc.get("last_success_size")))); // 確保對應大小
                    }

                    return acc;
                }, (acc1, acc2) -> acc1);

        Map<String, Object> result = new HashMap<>();
        result.put("backupDetail", combinedData); // 網安智檢問卷
        result.put("backupFailedData", backupFailedData); // 近30天數據備份失敗

        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse getWarningDetail(String eid, String timeFrom, String timeTo) {
        LocalDateTime now = DateUtil.getLocalNow();
        String from = getTimeFrom(now, timeFrom);
        String to = getTimeTo(now, timeTo);

        StringBuilder sb = new StringBuilder();

        // 統計嚴重漏洞設備數量(統計MS17010EventModel、CVE20200796EventModel、CVE20201472EventModel、AsiaInfoVuln，以deviceId去重)
        sb.append("WITH fatalRiskDevices AS (");
        sb.append("  SELECT COUNT(DISTINCT deviceId) AS deviceCount");
        sb.append("  FROM (");
        sb.append("    SELECT DISTINCT deviceId FROM servicecloud.MS17010EventModel ");
        sb.append("    WHERE eid = '").append(eid).append("'");
        sb.append("    AND deviceId IS NOT NULL AND deviceId != '' ");
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("    UNION ");
        sb.append("    SELECT DISTINCT deviceId FROM servicecloud.CVE20200796EventModel ");
        sb.append("    WHERE eid = '").append(eid).append("'");
        sb.append("    AND deviceId IS NOT NULL AND deviceId != '' ");
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("    UNION ");
        sb.append("    SELECT DISTINCT deviceId FROM servicecloud.CVE20201472EventModel ");
        sb.append("    WHERE eid = '").append(eid).append("'");
        sb.append("    AND deviceId IS NOT NULL AND deviceId != '' ");
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("    UNION ");
        sb.append("    SELECT DISTINCT device_id AS deviceId FROM servicecloud.AsiaInfoVuln ");
        sb.append("    WHERE eid = '").append(eid).append("' ");
        sb.append("    AND severity = 4 "); // 4 為嚴重級別
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("  ) AS fatalRiskDevices");
        sb.append("),");

        // 統計預警設備數量(以deviceId去重)
        sb.append("warningDevices AS (");
        sb.append("  SELECT COUNT(DISTINCT deviceId) AS deviceCount");
        sb.append("  FROM servicecloud.warning");
        sb.append("  WHERE eid = '").append(eid).append("' ");
        sb.append("  AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("),");

        // 統計弱密碼設備數量(統計windows、linux、sqlserver、oracle，以deviceId及source_db_id去重)
        sb.append("weakPasswordDevices AS (");
        sb.append("  SELECT COUNT(*) AS deviceCount");
        sb.append("  FROM (");
        sb.append("    SELECT DISTINCT deviceId, '' AS source_db_id FROM servicecloud.OperationSystemSecurityInfo");
        sb.append("    WHERE eid = '").append(eid).append("'");
        sb.append("    AND isPasswordPolicyUsing = 'false'");
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("    UNION ");
        sb.append("    SELECT deviceId, source_db_id FROM servicecloud.SQLServerPasswordPolicyRules ");
        sb.append("    WHERE eid = '").append(eid).append("'");
        sb.append("    AND isPolicyChecked = 'false'");
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("    GROUP BY deviceId, source_db_id");
        sb.append("    UNION ");
        sb.append("    SELECT deviceId, source_db_id FROM servicecloud.OraclePasswordPolicyRules ");
        sb.append("    WHERE eid = '").append(eid).append("'");
        sb.append("    AND isOraclePolicyChecked = 'false'");
        sb.append("    AND collectedTime BETWEEN '").append(from).append("' AND '").append(to).append("'");
        sb.append("    GROUP BY deviceId, source_db_id");
        sb.append("  ) AS weakPasswordDevices");
        sb.append(")");

        // 合併結果
        sb.append("SELECT ");
        sb.append("  fatalRiskDevices.deviceCount AS fatal_risk_device_count,");
        sb.append("  warningDevices.deviceCount AS warning_device_count,");
        sb.append("  weakPasswordDevices.deviceCount AS weak_password_device_count");
        sb.append(" FROM fatalRiskDevices, warningDevices, weakPasswordDevices");

        // 執行 SQL 並返回結果
        List<Map<String, Object>> result = bigDataUtil.srQuery(StringUtil.toString(sb));
        return BaseResponse.ok(result);
    }

    private String getTimeFrom(LocalDateTime now, String timeFrom) {
        LocalDateTime thirtyDaysBefore = now.minusDays(30);
        String thirtyDaysBeforeString = DateUtil.getSomeDateFormatString(thirtyDaysBefore, DateUtil.DATE_TIME_FORMATTER);
        return StringUtil.isNotEmpty(timeFrom) ? timeFrom : thirtyDaysBeforeString;
    }

    private String getTimeTo(LocalDateTime now, String timeTo) {
        String nowString = DateUtil.getSomeDateFormatString(now, DateUtil.DATE_TIME_FORMATTER);
        return StringUtil.isNotEmpty(timeTo) ? timeTo : nowString;
    }

    // oracle 資料庫備份失敗詳情
    private List<Map<String, Object>> getOracleDbBackupFailedDetail(String eid, String timeFrom, String timeTo) {
        StringBuilder sb = new StringBuilder();

        // 查詢備份失敗的次數
        sb.append("WITH OracleBackupFailed AS (");
        sb.append("  SELECT eid, COUNT(*) AS backup_failed_count");
        sb.append("  FROM servicecloud.T100BackupStatusCheck_sr_primary");
        sb.append("  WHERE eid = '").append(eid).append("'");
        sb.append("  AND backUpCheck != 'success'");
        sb.append("  AND collectedTime BETWEEN '").append(timeFrom).append("' AND '").append(timeTo).append("'");
        sb.append("  GROUP BY eid");
        sb.append("),");

        // 計算最近一次備份成功的時間與大小
        sb.append("LastSuccessBackup AS (");
        sb.append("  SELECT eid, collectedTime AS last_success_time, backUpSize AS last_success_size");
        sb.append("  FROM servicecloud.T100BackupStatusCheck_sr_primary");
        sb.append("  WHERE eid = '").append(eid).append("'");
        sb.append("  AND backUpCheck = 'success'");
        sb.append("  AND collectedTime BETWEEN '").append(timeFrom).append("' AND '").append(timeTo).append("'");
        sb.append("),");

        // 查詢備份成功的次數與總大小
        sb.append("SuccessStats AS (");
        sb.append("  SELECT eid, COUNT(*) AS success_count, SUM(backUpSize) AS total_success_size");
        sb.append("  FROM servicecloud.T100BackupStatusCheck_sr_primary");
        sb.append("  WHERE eid = '").append(eid).append("'");
        sb.append("  AND backUpCheck = 'success'");
        sb.append("  AND collectedTime BETWEEN '").append(timeFrom).append("' AND '").append(timeTo).append("'");
        sb.append("  GROUP BY eid");
        sb.append(")");

        // 組合
        sb.append("SELECT CAST(f.eid AS STRING) eid, CAST(f.backup_failed_count AS STRING) backup_failed_count, 'oracle' AS dbType,");
        sb.append("  ls.last_success_time,");
        sb.append("  CAST(COALESCE(ls.last_success_size, 0) AS STRING) AS last_success_size,");
        sb.append("  CAST(COALESCE(ss.success_count, 0) AS STRING) AS success_count,");
        sb.append("  CAST(COALESCE(ss.total_success_size, 0) AS STRING) AS total_success_size");
        sb.append(" FROM OracleBackupFailed f");
        sb.append(" LEFT JOIN LastSuccessBackup AS ls ON ls.eid = f.eid");
        sb.append(" LEFT JOIN SuccessStats AS ss ON ss.eid = f.eid");

        return bigDataUtil.srQuery(StringUtil.toString(sb));
    }

    // sqlserver 資料庫備份失敗詳情
    private List<Map<String, Object>> getSqlserverDbBackupFailedDetail(String eid, String timeFrom, String timeTo) {
        StringBuilder sb = new StringBuilder();

        // 計算最近一次備份成功的時間與大小
        sb.append("WITH LastSuccessBackup AS (");
        sb.append("  SELECT eid, collectedTime AS last_success_time, sqlserver_biz_sbh_backup_size AS last_success_size");
        sb.append("  FROM servicecloud.sqlserver_biz_sbh");
        sb.append("  WHERE eid = '").append(eid).append("'");
        sb.append("  AND collectedTime BETWEEN '").append(timeFrom).append("' AND '").append(timeTo).append("'");
        sb.append("  ORDER BY collectedTime DESC LIMIT 1");
        sb.append("),");

        // 查詢備份成功的次數與總大小
        sb.append("SuccessStats AS (");
        sb.append("  SELECT eid, COUNT(*) AS success_count, SUM(sqlserver_biz_sbh_backup_size) AS total_success_size");
        sb.append("  FROM servicecloud.sqlserver_biz_sbh");
        sb.append("  WHERE eid = '").append(eid).append("'");
        sb.append("  AND collectedTime BETWEEN '").append(timeFrom).append("' AND '").append(timeTo).append("'");
        sb.append("  GROUP BY eid");
        sb.append(")");

        // 組合
        sb.append("SELECT '").append(eid).append("' AS eid,");
        sb.append("  CAST(0 AS STRING) backup_failed_count,");
        sb.append("  'sqlserver' AS dbType,");
        sb.append("  ls.last_success_time,");
        sb.append("  CAST(COALESCE(ls.last_success_size, 0) AS STRING) AS last_success_size,");
        sb.append("  CAST(COALESCE(ss.success_count, 0) AS STRING) AS success_count,");
        sb.append("  CAST(COALESCE(ss.total_success_size, 0) AS STRING) AS total_success_size");
        sb.append(" FROM LastSuccessBackup ls");
        sb.append(" LEFT JOIN SuccessStats ss ON ss.eid = ls.eid");

        return bigDataUtil.srQuery(StringUtil.toString(sb));
    }
}
