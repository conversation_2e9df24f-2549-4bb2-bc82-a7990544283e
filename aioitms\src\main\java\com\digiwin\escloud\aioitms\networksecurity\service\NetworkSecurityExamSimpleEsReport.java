package com.digiwin.escloud.aioitms.networksecurity.service;

import com.digiwin.escloud.aioitms.es.service.EsService;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsEsReportRecord;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsReportRecord;
import com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExamSimpleEsReportRecord;
import com.digiwin.escloud.aioitms.networksecurity.model.emnus.NetworkReportType;
import com.digiwin.escloud.common.util.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class NetworkSecurityExamSimpleEsReport extends EsService<NetworkSecurityExamSimpleEsReportRecord> {
    @PostConstruct
    public void init() {
        indexCreate(NetworkSecurityExamSimpleEsReportRecord.class);
    }

    public String generateReport(AiopsExamRecordsReportRecord reportRecord) {
        NetworkSecurityExamSimpleEsReportRecord report = new NetworkSecurityExamSimpleEsReportRecord();
        BeanUtils.copyProperties(reportRecord, report);
        report.setId(reportRecord.getId() + "");
        report.setReportType(NetworkReportType.Simple.name());
        report.setReportDate(reportRecord.getReportDate().format(DateUtil.DATE_FORMATTER));
        NetworkSecurityExamSimpleEsReportRecord save = save(report, false);
        return save.getId();
    }

    public NetworkSecurityExamSimpleEsReportRecord getReport(String id) {
        NetworkSecurityExamSimpleEsReportRecord byId = findById(id, NetworkSecurityExamSimpleEsReportRecord.class);
        return byId;
    }

    public void deleteReport(String id) {
        deleteById(id,NetworkSecurityExamSimpleEsReportRecord.class);
    }

    public void updateReport(String id, String fieldPath, Object value) {
        updateByScript(id, fieldPath, value, NetworkSecurityExamSimpleEsReportRecord.class,null);
    }
} 