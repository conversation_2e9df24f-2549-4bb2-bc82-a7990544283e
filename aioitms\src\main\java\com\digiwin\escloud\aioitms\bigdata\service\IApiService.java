package com.digiwin.escloud.aioitms.bigdata.service;

import com.digiwin.escloud.aioitms.bigdata.SaveParams;
import com.digiwin.escloud.aioitms.bigdata.model.ApiParam;
import com.digiwin.escloud.aioitms.bigdata.model.Query;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-11-18 16:21
 * @Description
 */
public interface IApiService {

    Map<String, Object> dataQuery(String apiCode, int pageNum, int pageSize, ApiParam apiParam);
    void dataMapping(List<Map<String,Object>> sourceData, String sinkType, String sinkName,String schemaName );

    void queryReBuild(Query query);

    void queryBuildByAuth(Query query);

    ResponseBase saveData(SaveParams saveParams);

    BaseResponse saveDataStream(SaveParams saveParams);

    /**
     * 根据条件 删除OracleLinuxDiskio 模型的数据
     * @param saveParams
     * @return
     */
    ResponseBase deleteOracleLinuxDiskioData(Long eid,String deviceId,String collectedTime);

    ResponseBase getAsiaVulnInfo(String deviceId, String startTime, String endTime,Long eid);

    ResponseBase queryDiskDayGrowth(String deviceId);

    /**
     * 根據條件 查詢資料庫備份失敗詳情
     * @param eid 租戶Id
     * @param timeFrom 開始時間
     * @param timeTo 結束時間
     * @return BaseResponse
     */
    BaseResponse getDbBackupFailedDetail(String eid, String timeFrom, String timeTo);

    /**
     * 根據條件 查詢指定租戶存在預警詳情
     * @param eid 租戶Id
     * @param timeFrom 開始時間
     * @param timeTo 結束時間
     * @return BaseResponse
     */
    BaseResponse getWarningDetail(String eid, String timeFrom, String timeTo);
}
