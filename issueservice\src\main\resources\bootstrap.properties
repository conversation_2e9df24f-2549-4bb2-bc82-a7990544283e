spring.application.name=issue
## Nacos config
spring.cloud.nacos.discovery.server-addr=172.16.1.189:31848
spring.cloud.nacos.discovery.namespace=chenchaoe
spring.cloud.nacos.discovery.group=DEFAULT_GROUP
spring.cloud.nacos.discovery.metadata.name=${spring.application.name}
spring.cloud.nacos.config.server-addr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.nacos.config.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.nacos.config.group=${spring.cloud.nacos.discovery.group}
issue.kb.indepthai.code=1


