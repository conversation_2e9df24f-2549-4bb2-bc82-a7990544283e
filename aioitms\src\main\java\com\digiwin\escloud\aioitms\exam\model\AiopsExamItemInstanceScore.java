package com.digiwin.escloud.aioitms.exam.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.digiwin.escloud.aioitms.instance.model.AiopsItem;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 体检实例
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "AiopsExamItemInstanceScore对象", description = "体检实例")
public class AiopsExamItemInstanceScore  extends AiopsExamBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("体检记录id")
    private Long aerId;


    private String aiopsItem;

    private String aiopsItemId;

    @ApiModelProperty("是否完成")
    private Boolean examComplete;
    private ExamInstanceStatus examInstanceStatus;

    @ApiModelProperty("计算时间")
    private LocalDateTime scoreTime;

    private String levelCode;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    private CommonDeviceData aiopsDevice;

    private String aiopsItemCount;
    private List<AiopsExamInstanceIndexTypeScore> aeiitsList;
    //计分最小单位
    private List<AiopsExamInstanceIndexScore> aeInstanceIndexScoreList;

    // 报告记录查询排序字段
    private Long aeimId;

    private String networkExamCategoryCode;
    private String modelCode;
    private String categoryCode;
    private Long networkExamAssetId;
    private String deviceName;

    private List<AiopsItem> aiList;
    private List<NetworkSecurityExaminationProjectType> nseptList;

    // 運維項目數量
    private int aiopsItemTotal;

    public enum ExamInstanceStatus{
        NOT_START,
        EXAM_COMPLETE,
        EXAM_FAIL,
    }


}
