package com.digiwin.escloud.aioitms.report.controller;

import com.digiwin.escloud.aioitms.common.Constants;
import com.digiwin.escloud.aioitms.report.annotation.ProductServiceReportLog;
import com.digiwin.escloud.aioitms.report.constant.ReportConst;
import com.digiwin.escloud.aioitms.report.model.serviceReport.*;
import com.digiwin.escloud.aioitms.report.service.serviceReprot.ProductServiceReportService;
import com.digiwin.escloud.aioitms.report.service.serviceReprot.TProductServiceReportService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * @Date 2023/6/27 14:20
 * @Created yanggld
 * @Description
 */
@RestController
@RequestMapping("/product/service/report")
public class ProductServiceReportController {

    @Autowired
    private ProductServiceReportService productServiceReportService;

    @Autowired
    private TProductServiceReportService tproductServiceReportService;

    @ApiOperation(value = "保存报告")
    @PostMapping("/save")
    public void saveReport(@RequestParam(required = false, defaultValue = "37") String productCode, @RequestBody Map<String, Object> map) throws Exception {
        if (ReportConst.E_PRODUCT_CODE_LIST.contains(productCode)) {
            productServiceReportService.save(map);
        } else if (ReportConst.T_PRODUCT_CODE_LIST.contains(productCode)) {
            tproductServiceReportService.save(map);
        }
    }

    @ApiOperation(value = "生成报告")
    @PostMapping
    public Long generateReport(@RequestBody ProductServiceReportGenerateReqDTO model) throws Exception {
        return productServiceReportService.generate(model);
    }

    @ApiOperation(value = "分页查询报告")
    @GetMapping
    public PageInfo list(ProductServiceReportQueryDTO pageQueryDto) {
        PageInfo pageInfo = productServiceReportService.pageList(pageQueryDto);
        return pageInfo;
    }

    @ApiOperation(value = "通过id查询报告")
    @GetMapping("/{id}")
    public Object findById(@PathVariable String id) {
        ProductServiceReportModel productServiceReportModel = productServiceReportService.getById(Long.parseLong(id));
        String productCode = productServiceReportModel.getProductCode();
        if (ReportConst.E_PRODUCT_CODE_LIST.contains(productCode)) {
            ProductServiceReport productServiceReport = productServiceReportService.findById(id);
            ProductServiceReportRespDTO respDTO = new ProductServiceReportRespDTO();
            BeanUtils.copyProperties(productServiceReport, respDTO);
            respDTO.setAnalysisProjectCode(productServiceReportModel.getAnalysisProjectCode());
            return respDTO;
        } else if (ReportConst.T_PRODUCT_CODE_LIST.contains(productCode)) {
            TProductServiceReport tProductServiceReport = tproductServiceReportService.findById(id);
            tProductServiceReport.setServiceCode(productServiceReportModel.getServiceCode());

            return tProductServiceReport;
        }
        return null;
    }


    @ProductServiceReportLog(op = ProductServiceReportOperation.Delete)
    @ApiOperation(value = "通过id删除报告")
    @DeleteMapping
    public boolean deleteById(@RequestBody ProductServiceReportReqDTO dto) throws Exception {
        return productServiceReportService.deleteByState(dto.getId());
    }


    @ProductServiceReportLog(op = ProductServiceReportOperation.SubmitAudit)
    @ApiOperation(value = "提交审核")
    @PostMapping("/submit/audit")
    public boolean submitAudit(@RequestBody ProductServiceReportReqDTO dto) throws Exception {
        return productServiceReportService.submitAuditByState(dto.getProductServiceReportModel());
    }

    @ProductServiceReportLog(op = ProductServiceReportOperation.Audit)
    @ApiOperation(value = "审核")
    @PostMapping("/audit")
    public boolean audit(@RequestBody ProductServiceReportModel model) throws Exception {
        model.setAuditTime(LocalDateTime.now());
        return productServiceReportService.auditByState(model);
    }

    @ProductServiceReportLog(op = ProductServiceReportOperation.Reject)
    @ApiOperation(value = "退回")
    @PostMapping("/reject")
    public boolean reject(@RequestBody ProductServiceReportModel model) throws Exception {
        model.setRejectTime(LocalDateTime.now());
        return productServiceReportService.rejectByState(model);
    }

    @ProductServiceReportLog(op = ProductServiceReportOperation.SendPlatform)
    @ApiOperation(value = "发送平台")
    @PostMapping("/sendPlatform")
    public boolean sendPlatform(@RequestBody ProductServiceReportReqDTO dto) throws Exception {
        dto.setSendTime(LocalDateTime.now());
        return productServiceReportService.sendPlatformByState(dto.getProductServiceReportModel());
    }

    @ProductServiceReportLog(op = ProductServiceReportOperation.SendMail)
    @ApiOperation(value = "发送邮件")
    @PostMapping("/sendMail")
    public boolean sendMail(@RequestBody ProductServiceReportSendDTO dto) throws Exception {
        dto.setSendTime(LocalDateTime.now());
        return productServiceReportService.sendMailByState(dto);
    }

    @ProductServiceReportLog(op = ProductServiceReportOperation.SendMailAndPlatform)
    @ApiOperation(value = "发送邮件和平台")
    @PostMapping("/sendMailAndPlatform")
    public boolean sendMailAndPlatform(@RequestBody ProductServiceReportSendDTO dto) throws Exception {
        dto.setSendTime(LocalDateTime.now());
        return productServiceReportService.sendMailAndPlatformByState(dto);
    }


    @ApiOperation(value = "操作日志")
    @GetMapping("/operation/log")
    public List<ProductServiceReportOperationLog> operationLog(ProductServiceReportModel model) {
        return productServiceReportService.getOperationLog(model.getId());
    }


    @ApiOperation(value = "分析项目")
    @GetMapping("/analysis/project")
    public List<ProductServiceReportAnalysisProject> analysisProject() {
        return productServiceReportService.getAnalysisProject();
    }
}
