package com.digiwin.escloud.aiouser.service.invite;

import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.model.common.InvitationGetResponse;
import com.digiwin.escloud.common.model.ResponseCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2025-05-19 17:43
 * @Description
 */
@Service("ACTIVATE")
public class ActivateInvitation implements IHandleInvitation {
    @Value("${digiwin.aio.user.activate.effective.days:30}")
    private int effectiveDays;
    @Override
    public InvitationGetResponse buildInvitation(InvitationGetResponse res) {
        Invitation invitation = res.getInvitation();
        Date invitationDate = invitation.getInvitationDate();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(invitationDate);
        calendar.add(Calendar.DATE, effectiveDays);
        boolean isAfter = calendar.getTime().after(new Date());
        invitation = isAfter ? invitation : null;
        res.setInvitation(invitation);
        if (!isAfter) {
            res.setCode(ResponseCode.INVITATION_IS_INVALID.getCode());
            res.setErrMsg(ResponseCode.INVITATION_IS_INVALID.getMsg());
        }
        return res;
    }
}
