<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.digiwin.escloud.aiobasic.edr.dao.EdrReportMapper">
    <resultMap id="reportRecordMapNew" type="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="customerName" column="customerName"/>
        <result property="orgCnNames" column="orgCnNames"/>
        <result property="orgIds" column="orgIds"/>
        <result property="reportGenerateType" column="reportGenerateType"/>
        <result property="reportStartTime" column="reportStartTime"/>
        <result property="reportEndTime" column="reportEndTime"/>
        <result property="reportGenerateTime" column="reportGenerateTime"/>
        <result property="userId" column="userId"/>
        <result property="userName" column="userName"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="fileName" column="fileName"/>
        <result property="description" column="description"/>
        <result property="sendMethod" column="sendMethod"/>
        <result property="appointmentTime" column="appointmentTime"/>
        <result property="sendStatus" column="sendStatus"/>
        <result property="auto" column="auto"/>
        <result property="modelVersion" column="modelVersion"/>
        <result property="dbType" column="dbType"/>
        <result property="reportType" column="reportType"/>
        <result property="dbId" column="dbId"/>
        <result property="deviceId" column="deviceId"/>
        <association property="sendLog" columnPrefix="s_" resultMap="sendLogMap" />
        <collection property="files" column="{id=id}" javaType="ArrayList" select="getReportFile"/>
    </resultMap>
    <resultMap id="sendLogMap" type="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordSendLog">
        <result property="id" column="id"/>
        <result property="errId" column="errId"/>
        <result property="sendTime" column="sendTime"/>
        <collection property="receiverList" resultMap="receiverMap" columnPrefix="r_" />
    </resultMap>
    <resultMap id="reportRecordMap" type="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="customerName" column="customerName"/>
        <result property="orgCnNames" column="orgCnNames"/>
        <result property="orgIds" column="orgIds"/>
        <result property="reportGenerateType" column="reportGenerateType"/>
        <result property="reportStartTime" column="reportStartTime"/>
        <result property="reportEndTime" column="reportEndTime"/>
        <result property="reportGenerateTime" column="reportGenerateTime"/>
        <result property="userId" column="userId"/>
        <result property="userName" column="userName"/>
        <result property="sendStatus" column="sendStatus"/>
        <result property="sendTime" column="sendTime"/>
        <result property="type" column="type"/>
        <result property="typeName" column="typeName"/>
        <result property="url" column="url"/>
        <result property="fileName" column="fileName"/>
        <result property="description" column="description"/>
        <result property="sendMethod" column="sendMethod"/>
        <result property="appointmentTime" column="appointmentTime"/>
        <result property="modelVersion" column="modelVersion"/>
        <result property="auto" column="auto"/>
        <collection property="receiversList" column="{id=id}" javaType="ArrayList" select="getReceivers"/>
        <collection property="files" column="{id=id}" javaType="ArrayList" select="getReportFile"/>
    </resultMap>

    <resultMap id="ReportRecordAutoSet" type="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordAutoSet">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="customerName" column="customerName"/>
        <result property="orgCnNames" column="orgCnNames"/>
        <result property="orgIds" column="orgIds"/>
        <result property="reportGenerateType" column="reportGenerateType"/>
        <result property="type" column="type"/>
        <result property="cycle" column="cycle"/>
        <result property="day" column="day"/>
        <result property="url" column="url"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="sourceId" column="sourceId"/>
        <result property="deviceId" column="deviceId"/>
        <result property="modelVersion" column="modelVersion"/>
        <result property="processUserId" column="processUserId"/>
        <result property="processUserName" column="processUserName"/>
        <collection property="reportRecordAutoSetReceivers" resultMap="ReportRecordAutoSetReceivers" columnPrefix="r_" />
    </resultMap>
    <resultMap id="ReportRecordAutoSetReceivers" type="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordAutoSetReceivers">
        <result property="id" column="id"/>
        <result property="errasId" column="errasId"/>
        <association property="receivers" columnPrefix="rr_" resultMap="receiverMap" />
    </resultMap>
    <resultMap id="receiverMap" type="com.digiwin.escloud.aiobasic.edr.model.base.ReportReceiver">
        <result property="id" column="id"/>
        <result property="sid" column="sid"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="customerName" column="customerName"/>
        <result property="receiverName" column="receiverName"/>
        <result property="receiverMail" column="receiverMail"/>
        <result property="title" column="title"/>
    </resultMap>
    <select id="getReceivers" resultType="java.util.Map">
        SELECT rr.id, rr.receiverName, rr.title, errslr.receiverMail
        FROM report_receiver rr
        INNER JOIN edr_report_record_send_log_receivers errslr ON rr.id = errslr.rrId
        INNER JOIN edr_report_record_send_log errsl ON errslr.errslId = errsl.id
        WHERE errId=#{id}
        GROUP BY rr.id
    </select>

    <select id="getRecordReceivers" resultType="java.util.Map">
        SELECT errslr.receiverMail
        FROM edr_report_record_send_log_receivers errslr
        INNER JOIN edr_report_record_send_log errsl ON errslr.errslId = errsl.id
        WHERE errId=#{errId}
    </select>

    <select id="getReportRecords" resultMap="reportRecordMap">
        SELECT a.id,a.sid,a.serviceCode,a.customerName,a.orgCnNames,a.orgIds,a.reportGenerateType,a.reportStartTime,a.reportEndTime,a.reportGenerateTime,a.userId,a.userName, a.auto,
        a.type,b.name typeName,a.url,a.fileName,a.description,a.sendMethod,a.appointmentTime,a.sendStatus, c.sendTime, a.modelVersion
        FROM edr_report_record a
        left join edr_report_type b on b.code = a.type
        LEFT JOIN (
            SELECT errsl1.errId,errsl1.sendTime
            FROM edr_report_record_send_log errsl1,(SELECT errId,MAX(sendTime) AS sendTime FROM edr_report_record_send_log GROUP BY errId) errsl2
            WHERE errsl1.errId=errsl2.errId AND errsl1.sendTime=errsl2.sendTime
            GROUP BY errsl1.errId
        ) c ON c.errId = a.id
        WHERE 1=1
        <if test="id != null">
            and a.id = #{id}
        </if>
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="sendStatus!=null and sendStatus!=''">
            and a.sendStatus in (${sendStatus})
        </if>
        <if test="type!=null and type!=''">
            and a.type in (${type})
        </if>
        <if test="orgCnNames!=null and orgCnNames!=''">
            and a.orgCnNames like CONCAT("%",#{orgCnNames},"%")
        </if>
        <if test="userName!=null and userName!=''">
            and a.userName like CONCAT("%",#{userName},"%")
        </if>
        <if test="sendTimeStart!=null and sendTimeStart!='' and sendTimeEnd!=null and sendTimeEnd!=''">
            and exists (SELECT 1 FROM edr_report_record_send_log log WHERE log.errId = a.id and log.sendTime BETWEEN #{sendTimeStart} and #{sendTimeEnd})
        </if>
        <if test="modelVersion != null and modelVersion != ''">
            and (
            <if test="modelVersion == '1.0'">
                a.modelVersion IS NULL or a.modelVersion = '' or
            </if>
            a.modelVersion = #{modelVersion})
        </if>
        <if test="reportGenerateTimeStart!=null and reportGenerateTimeStart!='' and reportGenerateTimeEnd!=null and reportGenerateTimeEnd!=''">
            and a.reportGenerateTime between #{reportGenerateTimeStart} and #{reportGenerateTimeEnd}
        </if>
        order by a.sid asc,a.reportGenerateTime desc
        <if test="size>0">
            LIMIT #{start} , #{size}
        </if>
    </select>

    <select id="getReportRecordDetail" resultMap="reportRecordMapNew">
        SELECT err.id,err.sid,err.serviceCode,err.customerName,err.orgCnNames,err.orgIds,err.reportGenerateType,err.reportStartTime,err.reportEndTime,err.reportGenerateTime,err.userId,err.userName,err.`type`,err.url,err.fileName,ifnull(err.description,'') description,err.sendMethod,err.appointmentTime,err.sendStatus,err.auto, err.modelVersion,
        errsl.id s_id ,errsl.errId s_errId,'' s_sendTime,
        rr.id s_r_id,rr.sid s_r_sid,rr.serviceCode s_r_serviceCode,rr.receiverName s_r_receiverName,rr.receiverMail s_r_receiverMail,rr.title s_r_title
        from edr_report_record err
        LEFT JOIN (SELECT max(id) id,errId FROM edr_report_record_send_log group by errId )  errsl ON err.id = errsl.errId
        LEFT JOIN edr_report_record_send_log_receivers errslr ON errslr.errslId = errsl.id
        LEFT JOIN report_receiver rr ON rr.id = errslr.rrId
        WHERE 1=1
        <if test="id > 0">
            and err.id = #{id}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and err.serviceCode = #{serviceCode}
        </if>
        <if test="reportStartTime!=null and reportStartTime!=''">
            and err.reportStartTime = #{reportStartTime}
        </if>
        <if test="reportEndTime!=null and reportEndTime!=''">
            and err.reportEndTime = #{reportEndTime}
        </if>
        <if test="auto!=null and auto!=''">
            and err.auto = #{auto}
        </if>
        <if test="siteIds!=null and siteIds!=''">
            and err.orgIds = #{siteIds}
        </if>
        <if test="modelVersion!=null and modelVersion!=''">
            and err.modelVersion = #{modelVersion}
        </if>
    </select>
    <select id="getLatestSendLog" resultType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordSendLog">
        SELECT errsl1.errId,errsl1.id,errsl1.sendTime
        FROM edr_report_record_send_log errsl1,(SELECT errId,MAX(id) AS id FROM edr_report_record_send_log GROUP BY errId) errsl2
        WHERE errsl1.errId=errsl2.errId AND errsl1.id=errsl2.id
        AND errsl1.errId =#{id}
        GROUP BY errsl1.errId
    </select>
    <select id="getReceiverList" resultType="com.digiwin.escloud.aiobasic.edr.model.base.ReportReceiver">
        SELECT a.*
        FROM report_receiver a
        left join edr_report_record_send_log_receivers b on b.rrId = a.id
        where b.errslId = #{id}
    </select>

    <select id="getReportTypes" resultType="java.util.Map">
        SELECT code, name from edr_report_type
        order by id asc
    </select>

    <select id="getReportRecordCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM edr_report_record a
        WHERE 1=1
        <if test="id != null">
            and a.id = #{id}
        </if>
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="sendStatus!=null and sendStatus!=''">
            and a.sendStatus in (${sendStatus})
        </if>
        <if test="type!=null and type!=''">
            and a.type in (${type})
        </if>
        <if test="orgCnNames!=null and orgCnNames!=''">
            and a.orgCnNames like CONCAT("%",#{orgCnNames},"%")
        </if>
        <if test="userName!=null and userName!=''">
            and a.userName like CONCAT("%",#{userName},"%")
        </if>
        <if test="sendTimeStart!=null and sendTimeStart!='' and sendTimeEnd!=null and sendTimeEnd!=''">
            and exists (SELECT 1 FROM edr_report_record_send_log log WHERE log.errId = a.id and log.sendTime BETWEEN #{sendTimeStart} and #{sendTimeEnd})
        </if>
        <if test="reportGenerateTimeStart!=null and reportGenerateTimeStart!='' and reportGenerateTimeEnd!=null and reportGenerateTimeEnd!=''">
            and a.reportGenerateTime between #{reportGenerateTimeStart} and #{reportGenerateTimeEnd}
        </if>
        <if test="modelVersion != null and modelVersion != ''">
            and (
            <if test="modelVersion == '1.0'">
                a.modelVersion IS NULL or a.modelVersion = '' or
            </if>
            a.modelVersion = #{modelVersion})
        </if>
    </select>

    <insert id="generateReportRecord" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord">
        insert into edr_report_record(id,sid,serviceCode,customerName,orgCnNames,orgIds,reportGenerateType,reportStartTime,reportEndTime,
        reportGenerateTime,userId,userName, url, fileName, description, `type`, sendMethod, appointmentTime, sendStatus, auto, modelVersion)
        values(#{id}, #{sid}, #{serviceCode}, #{customerName}, #{orgCnNames}, #{orgIds}, #{reportGenerateType}, #{reportStartTime}, #{reportEndTime},
         #{reportGenerateTime}, #{userId}, #{userName}, #{url}, #{fileName}, #{description}, #{type}, #{sendMethod}, #{appointmentTime}, #{sendStatus}, #{auto}, #{modelVersion})
    </insert>

    <update id="editReportRecord" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord">
        update edr_report_record
        set sid = #{sid},serviceCode = #{serviceCode},customerName = #{customerName},orgCnNames = #{orgCnNames},orgIds = #{orgIds},reportGenerateType = #{reportGenerateType},
        reportStartTime = #{reportStartTime},reportEndTime = #{reportEndTime},reportGenerateTime = #{reportGenerateTime},userId = #{userId},userName = #{userName},
        url = #{url},fileName = #{fileName},description = #{description},`type` = #{type},sendMethod = #{sendMethod},appointmentTime = #{appointmentTime},sendStatus = #{sendStatus}
        where id = #{id}
    </update>

    <delete id="deleteReportRecord">
        DELETE FROM edr_report_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteReportRecordLog">
        DELETE FROM edr_report_record_send_log
        WHERE errId = #{id}
    </delete>

    <delete id="deleteReportReceivers">
        DELETE FROM edr_report_record_send_log_receivers
        WHERE errslId in (select id from edr_report_record_send_log where errId = #{id})
    </delete>
    <update id="updateFieldValue">
        update ${tableName}
        set ${fieldName}= #{newValue}
        WHERE ${fieldName} = #{oldValue}
    </update>

    <select id="getReportRecordStatus" resultType="java.lang.String">
        SELECT a.sendStatus
        FROM edr_report_record a
        WHERE a.id = #{id}
    </select>
    <insert id="saveReportSendLog" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordSendLog">
        insert into edr_report_record_send_log (id,errId, sendTime)
        values(#{id}, #{errId}, #{sendTime})
    </insert>

    <insert id="editReportSendLog" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordSendLog">
        update edr_report_record_send_log
        set errId = #{errId}, sendTime = #{sendTime}
        where id = #{id}
    </insert>
    <delete id="deleteReportSendLogReceivers" >
        delete from edr_report_record_send_log_receivers where errslId = #{id}
    </delete>

    <insert id="saveReportSendLogReceivers" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordSendLogReceivers">
        insert into edr_report_record_send_log_receivers (errslId, rrId, receiverMail)
        values
        <foreach collection="receiverList" item="item" separator=",">
            (#{errslId}, #{item.id}, #{item.receiverMail})
        </foreach>
    </insert>
    <insert id="updateReportSendLog" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecord">
        update edr_report_record_send_log
        set sendTime =#{sendTime}, receivers =#{receivers},
        where id = #{id}
    </insert>

    <select id="getAndAppointmentReport" resultMap="reportRecordMapNew">
        SELECT distinct err.id,err.sid,err.serviceCode,err.customerName,err.orgCnNames,err.orgIds,err.reportGenerateType,
               err.reportStartTime,err.reportEndTime,err.reportGenerateTime,err.userId,err.userName,err.`type`,err.url,err.fileName,
               ifnull(err.description,'') description,err.sendMethod,err.appointmentTime,err.sendStatus,err.auto, err.modelVersion,
		       errsl.id s_id ,errsl.errId s_errId,errsl.sendTime s_sendTime,
		       rr.id s_r_id,rr.sid s_r_sid,rr.serviceCode s_r_serviceCode,rr.receiverName s_r_receiverName,rr.receiverMail s_r_receiverMail,
               rr.title s_r_title,adrr.dbType,adrr.reportType,adrr.dbId,adrr.deviceId
        from edr_report_record_send_log errsl
        LEFT JOIN edr_report_record err ON err.id = errsl.errId
        LEFT JOIN edr_report_record_send_log_receivers errslr ON errslr.errslId = errsl.id
        LEFT JOIN report_receiver rr ON rr.id = errslr.rrId
        LEFT JOIN aiops_db_report_record adrr ON adrr.edrReportRecordId = err.id
        where
        err.sendMethod=1 and TIMESTAMPDIFF(SECOND ,NOW(),err.appointmentTime)<![CDATA[<=]]>0
        and ((err.sendStatus=2 and err.modelVersion in ('1.0', 'ORACLE', 'MSSQL')) OR (err.sendStatus=3 and err.modelVersion='2.0'))
        and (errsl.sendTime IS null or errsl.sendTime = '')
        <if test="serviceCodeList!=null and serviceCodeList.size > 0">
            and err.serviceCode IN (
                <foreach collection="serviceCodeList" item="serviceCode" separator=",">
                    #{serviceCode}
                </foreach>
            )
        </if>
        order by errsl.id desc
    </select>

    <update id="updateSendStatus"  >
        UPDATE edr_report_record
        SET sendStatus=#{sendStatus}
        WHERE id=#{id}
    </update>

    <update id="updateSendLog"  >
        UPDATE edr_report_record_send_log
        SET sendTime=#{sendTime}
        WHERE id=#{id}
    </update>

    <select id="getReportRecordAutoSets" resultMap="ReportRecordAutoSet">
        select a.id,a.sid,a.serviceCode,a.customerName,a.orgCnNames,a.orgIds,a.reportGenerateType,a.`type`,a.cycle,a.`day`,a.url,a.source,a.status, a.modelVersion,
        a.processUserId, a.processUserName,a.sourceId,a.deviceId,
        b.id r_id,b.errasId r_errasId,
        c.id r_rr_id,c.sid r_rr_sid,c.serviceCode r_rr_serviceCode,c.receiverName r_rr_receiverName,c.receiverMail r_rr_receiverMail,c.title r_rr_title
        from edr_report_record_auto_set a
        left join edr_report_record_auto_set_receivers b on a.id = b.errasId
        left join report_receiver c on c.id = b.rrId
        where 1=1 and status != -1
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="serviceCodeList != null and serviceCodeList.size() > 0">
            and a.serviceCode IN (
                <foreach collection="serviceCodeList" item="code" separator=",">
                    #{code}
                </foreach>
            )
        </if>
        <if test="type!=null and type!=''">
            and a.type= #{type}
        </if>
        <if test="status!=null and status!=''">
            and a.status= #{status}
        </if>
    </select>
    <select id="getReportRecordAutoSetDetail" resultMap="ReportRecordAutoSet">
        select a.id,a.sid,a.serviceCode,COALESCE(a.modelVersion, '') AS modelVersion,COALESCE(a.processUserId, '') AS processUserId,
        COALESCE(a.processUserName, '') AS processUserName,a.customerName,a.orgCnNames,a.orgIds,a.reportGenerateType,a.`type`,a.cycle,
        a.`day`,a.url,a.source,a.status,a.sourceId,b.id r_id,b.errasId r_errasId,c.id r_rr_id,c.sid r_rr_sid,
        c.serviceCode r_rr_serviceCode,c.receiverName r_rr_receiverName,c.receiverMail r_rr_receiverMail,c.title r_rr_title
        from edr_report_record_auto_set a
        left join edr_report_record_auto_set_receivers b on a.id = b.errasId
        left join report_receiver c on c.id = b.rrId
        where 1=1 and a.status != -1
        <if test="sid>0">
            and a.sid = #{sid}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="type!=null and type!=''">
            and a.type= #{type}
        </if>
        <if test="id!=null and id!=''">
            and a.id= #{id}
        </if>
        <if test="modelVersion != null and modelVersion != ''">
            and a.source = #{source} and a.modelVersion = #{modelVersion}
        </if>
        <if test="orgIds != null and orgIds != ''">
            <foreach collection="orgIds" item="orgId" open="and (" separator=" or " close=")">
                a.orgIds like '%${orgId}%'
            </foreach>
        </if>
        <if test="sourceId != null and sourceId != ''">
            and a.sourceId = #{sourceId}
        </if>

    </select>
    <update id="autoSet" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportRecordAutoSet">
       insert into edr_report_record_auto_set(id,sid,serviceCode,customerName,orgCnNames,orgIds,reportGenerateType,`type`,cycle,`day`,url,status, sourceId,source,modelVersion,processUserId,processUserName,deviceId)
       values(#{id}, #{sid}, #{serviceCode}, #{customerName}, #{orgCnNames}, #{orgIds}, #{reportGenerateType}, #{type}, #{cycle}, #{day}, #{url}, #{status}, #{sourceId}, #{source}, #{modelVersion}, #{processUserId}, #{processUserName}, #{deviceId})
       ON DUPLICATE KEY UPDATE sid = #{sid}, serviceCode = #{serviceCode}, customerName = #{customerName}, orgCnNames = #{orgCnNames},
        orgIds = #{orgIds}, reportGenerateType = #{reportGenerateType}, `type` = #{type}, cycle = #{cycle}, `day` = #{day},
        url = #{url}, status = #{status}, sourceId = #{sourceId}, source = #{source}, modelVersion = #{modelVersion}, processUserId = #{processUserId}, processUserName = #{processUserName}, deviceId = #{deviceId}
    </update>

    <delete id="deleteAutoSetReceivers">
        delete from edr_report_record_auto_set_receivers where errasId =#{id}
    </delete>

    <insert id="insertAutoSetReceivers">
        insert into edr_report_record_auto_set_receivers(errasId,rrId)
        values
        <foreach collection="list" item="item" separator=",">
            (#{errasId}, #{item})
        </foreach>
    </insert>
    <insert id="insertProcessRecord">
        insert into edr_org_collector_process_record(sid,eid,serverId,orgId,collectorId,processUserId,processUserName,enable
        <if test="operation!=''">
            ,operation
        </if>
        <if test="originalGroup!=''">
            ,originalGroup
        </if>
        <if test="currentGroup!=''">
            ,currentGroup
        </if>
        <if test="deleteReason!=''">
            ,deleteReason
        </if>
        <if test="agentChangeReason != ''">
            ,agentChangeReason
        </if>
        )
        values (#{sid},#{eid},#{serverId},#{orgId},#{collectorId},#{processUserId},#{processUserName},#{enable}
        <if test="operation!=''">
            ,#{operation}
        </if>
        <if test="originalGroup!=''">
            ,#{originalGroup}
        </if>
        <if test="currentGroup!=''">
            ,#{currentGroup}
        </if>
        <if test="deleteReason!=''">
            ,#{deleteReason}
        </if>
        <if test="agentChangeReason != ''">
            ,#{agentChangeReason}
        </if>
        )
    </insert>
    <insert id="insertProcessRecords"
            parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecordSaveDTO">
        insert into edr_org_collector_process_record(sid,eid,serverId,orgId,collectorId,processUserId,
                                                     processUserName,enable,operation,originalGroup,
                                                     currentGroup,deleteReason,agentChangeReason)
        values
        <foreach collection="dtoList" item="dto" separator=",">
            (#{dto.sid},#{dto.eid},#{dto.serverId},#{dto.orgId},#{dto.collectorId},#{dto.processUserId},
            #{dto.processUserName},#{dto.enable},#{dto.operation},#{dto.originalGroup},#{dto.currentGroup},
             #{dto.deleteReason},#{dto.agentChangeReason})
        </foreach>
    </insert>
    <select id="getProcessRecord"
            parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecord"
            resultType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecord">
        SELECT
        record.eid as 'eid',
        record.processUserId as 'processUserId',
        record.processUserName as 'processUserName',
        record.enable as 'enable',
        record.createTime as 'createTime',
        record.operation as 'operation',
        record.originalGroup as 'originalGroup',
        record.currentGroup as 'currentGroup',
        record.deleteReason as 'deleteReason',
        record.agentChangeReason as 'agentChangeReason'
        <if test="model.serverId == '365750770160999'">
            ,
            agent.endpointName as 'agentName',
            agent.lastReportedIP as 'IP'
        </if>
        FROM edr_org_collector_process_record record
        <if test="model.serverId == '365750770160999'">
            LEFT JOIN sentinelone_agents agent ON record.collectorId = agent.agentId
        </if>
        WHERE serverId = #{model.serverId}
        <if test="model.collectorId == null or model.collectorId == ''">
            AND record.eid = #{model.eid}
        </if>
        <if test="model.collectorId != null and model.collectorId != ''">
            AND collectorId = #{model.collectorId}
        </if>
        <if test="model.createTimeStart != null and model.createTimeEnd != null">
            AND createTime BETWEEN #{model.createTimeStart} AND #{model.createTimeEnd}
        </if>
        <if test="!operationParam.isEmpty() or !enableParam.isEmpty()">
            AND
            <trim prefix="(" suffix=")" prefixOverrides="AND|OR">
                <if test="operationParam.size() > 0">
                    <foreach collection="operationParam" item="operation" separator="OR" open="(" close=")">
                        LOWER(operation) LIKE LOWER(CONCAT('%',#{operation},'%'))
                    </foreach>
                </if>
                <if test="enableParam.size() > 0">
                    OR (enable IN (
                    <foreach collection="enableParam" item="enable" separator=",">
                        #{enable}
                    </foreach>
                    )
                    AND operation is null)
                </if>
            </trim>
        </if>

        ORDER BY createTime DESC;
    </select>
    <select id="getProcessRecordLatest"
            resultType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecord">
        SELECT *
        FROM edr_org_collector_process_record
        WHERE collectorId =#{collectorId} and serverId = #{serverId} and enable = 0
        ORDER BY createTime desc limit 1
    </select>
    <select id="getProcessRecordLatestList"
            resultType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecord">
        SELECT serverId,collectorId,max(createTime) createTime
        FROM edr_org_collector_process_record
        where enable = 0 and serverId = #{serverId} and collectorId in
        <foreach collection="collectorIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by serverId,collectorId
    </select>

    <insert id="updateDbReportStatus">
        update aiops_db_report_record
        set edrReportRecordId = #{edrReportRecordId}
        where id = #{dbReportId}
    </insert>

    <insert id="saveAutoProcessRecord" parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.AutoProcessRecord">
        INSERT INTO edr_report_record_auto_process_record (errasId, eid, processUserId, processUserName, enable, operation)
        VALUES (#{id},#{eid},#{processUserId},#{processUserName},#{enable},#{operation})
    </insert>

    <select id="getAutoProcessRecordList" resultType="com.digiwin.escloud.aiobasic.edr.model.edr.AutoProcessRecord">
        SELECT errasId as id, eid, processUserId,
        <choose>
            <when test="isMis == true">
                CASE
                    WHEN processUserId LIKE '%@digiwin.com' THEN
                    <choose>
                        <when test="area == 'TW'"> '鼎新人員' </when>
                        <when test="area == 'CN'"> '鼎捷人员' </when>
                        <otherwise> erras.processUserName </otherwise>
                    </choose>
                    WHEN processUserId IS NULL
                    OR processUserId = '' THEN
                    <choose>
                        <when test="area == 'TW'"> 'Ai智管家' </when>
                        <when test="area == 'CN'"> '智管家' </when>
                        <otherwise> erras.processUserName </otherwise>
                    </choose>
                    ELSE processUserName
                END AS processUserName,
            </when>
            <otherwise>
                CASE
                    WHEN processUserId IS NULL OR processUserId = '' THEN
                    <choose>
                        <when test="area == 'TW'"> 'Ai智管家' </when>
                        <when test="area == 'CN'"> '智管家' </when>
                        <otherwise> erras.processUserName </otherwise>
                    </choose>
                    ELSE processUserName
                END AS processUserName,
            </otherwise>
        </choose>
        enable, operation, createTime
        FROM edr_report_record_auto_process_record
        WHERE eid = #{eid} AND errasId = #{errasId}
        ORDER BY createTime DESC
    </select>

    <delete id="autoSetDel">
        UPDATE edr_report_record_auto_set SET status = -1 WHERE id = #{id}
    </delete>

    <select id = "getAutoMailSendList" parameterType="com.digiwin.escloud.aiobasic.edr.model.edr.AutoMailSendListParams"
            resultType="com.digiwin.escloud.aiobasic.edr.model.edr.AutoMailRecords">
        SELECT
            erras.id,
            t.eid eid,
            erras.serviceCode serviceCode,
            erras.source modelName,
            COALESCE(NULLIF(erras.modelVersion, ''), '1.0') AS modelVersion,
            erras.type reportType,
            erras.cycle reportCycle,
            erras.day sendDay,
            erras.status scheduleStatus,
            GROUP_CONCAT(DISTINCT rr.receiverMail ORDER BY rr.receiverMail SEPARATOR ';') AS receiverMail,
            <choose>
                <when test="isMis == true">
                    CASE
                        WHEN errapr.processUserId LIKE '%@digiwin.com' THEN
                            <choose>
                                <when test="area == 'TW'"> '鼎新人員' </when>
                                <when test="area == 'CN'"> '鼎捷人员' </when>
                                <otherwise> errapr.processUserName </otherwise>
                            </choose>
                        ELSE errapr.processUserName
                    END AS processUserName,
                </when>
                <otherwise>
                    errapr.processUserName,
                </otherwise>
            </choose>
            errapr_latest.createTime,
            erras.sourceId instance_id,
            IFNULL(add.dbInstanceName, '') AS instance_name,
            IFNULL(add2.dbDisplayName, '') AS instance_display_name,
            IFNULL(add.dbIpAddress, '') AS instance_ip_address,
            errapr.processUserId
        FROM
            edr_report_record_auto_set erras
            LEFT JOIN supplier_tenant_map t ON erras.serviceCode = t.serviceCode
            LEFT JOIN edr_report_record_auto_set_receivers errasr ON errasr.errasId = erras.id
            LEFT JOIN report_receiver rr ON rr.id = errasr.rrId
            LEFT JOIN edr_report_record_auto_process_record errapr ON errapr.errasId = erras.id AND errapr.operation = 'CREATE'
            LEFT JOIN (
                SELECT
                    errasId, MAX(createTime) AS createTime
                FROM
                    edr_report_record_auto_process_record
                GROUP BY
                    errasId
                ) errapr_latest ON errapr_latest.errasId = erras.id
            LEFT JOIN aiops_device_datasource `add` ON `add`.dbId = erras.sourceId
            LEFT JOIN (
                SELECT
                    dbId,
                    GROUP_CONCAT(DISTINCT dbDisplayName SEPARATOR ';') AS dbDisplayName
                FROM
                    aiops_device_datasource
                    INNER JOIN aiops_device ad2 ON adId = ad2.id
                    LEFT JOIN supplier_tenant_map t ON t.eid = ad2.eid
                WHERE
                    isDelete = 0
                AND
                    t.serviceCode = #{serviceCode}
                GROUP BY
                    dbId
                ) add2 ON `add`.dbId = add2.dbId
        WHERE
            erras.serviceCode = #{serviceCode} AND erras.status != -1
        <!-- 動態條件 -->
        <if test="modelCode != null and modelCode.size() > 0">
            AND erras.source IN
            <foreach collection="modelCode" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type.size() > 0">
            AND erras.type IN
            <foreach collection="type" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cycle != null and cycle.size() > 0" >
            AND erras.cycle IN
            <foreach collection="cycle" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null">
            AND erras.status = #{status}
        </if>
        <if test="processUserName != null and processUserName != ''">
            <choose>
                <when test="isMis == true">
                    AND (
                        (
                            erras.processUserId LIKE '%@digiwin.com'
                            AND (
                                (
                                <choose>
                                    <when test="area == 'TW'"> '鼎新人員' </when>
                                    <when test="area == 'CN'"> '鼎捷人员' </when>
                                    <otherwise> erras.processUserName </otherwise>
                                </choose>
                                ) LIKE CONCAT('%', #{processUserName}, '%')
                            )
                        )
                        OR (
                            erras.processUserId NOT LIKE '%@digiwin.com'
                            AND UPPER(erras.processUserName) LIKE CONCAT('%', UPPER(#{processUserName}), '%')
                        )
                    )
                </when>
                <otherwise>
                    AND UPPER(erras.processUserName) LIKE CONCAT('%', UPPER(#{processUserName}), '%')
                </otherwise>
            </choose>
        </if>
        <if test="receiverMail != null and receiverMail != ''">
            AND
            erras.id IN (
            SELECT
                errasr_sub.errasId
            FROM
                edr_report_record_auto_set_receivers errasr_sub
            WHERE
                errasr_sub.rrId IN (
                                    SELECT
                                        rr_sub.id
                                    FROM
                                        report_receiver rr_sub
                                    WHERE
                                        UPPER(rr_sub.receiverMail) LIKE concat('%',UPPER(#{receiverMail}),'%')
                                    )
            )
        </if>
        GROUP BY
            erras.id,eid,serviceCode,modelName,erras.modelVersion,reportType,reportCycle,sendDay,scheduleStatus,erras.processUserName
        ORDER BY
            errapr_latest.createTime DESC,
            erras.source,
            erras.type,
            erras.cycle,
            erras.day,
            erras.status
    </select>

    <insert id="saveReportFile" parameterType="com.digiwin.escloud.aiobasic.edr.model.base.ReportFile">
        INSERT INTO edr_report_record_file(id, sid, errId, fileId, url, name)
        VALUES
        <foreach collection="reportFiles" item="reportFile" separator=",">
            (#{reportFile.id}, #{reportFile.sid}, #{reportFile.errId}, #{reportFile.fileId}, #{reportFile.url}, #{reportFile.name})
        </foreach>
    </insert>

    <select id="getReportFile" resultType="com.digiwin.escloud.aiobasic.edr.model.base.ReportFile">
        SELECT id, sid, errId, fileId, url, name
        FROM edr_report_record_file
        WHERE errId = #{id}
    </select>

    <select id="getEdrv2EventIds" resultType="java.lang.String">
        SELECT eventId
        FROM edr_event_kb
        WHERE serverId = '365750770160999'
          AND (
            eventDesc IS NOT NULL
                OR issueCode IS NOT NULL
                OR status IS NOT NULL
                OR statusRemark IS NOT NULL
                OR releaseStatus IS NOT NULL
                OR releaseRemark IS NOT NULL
            )
    </select>

    <delete id="deleteReportFiles">
        DELETE FROM edr_report_record_file WHERE errId = #{errId}
    </delete>

    <delete id="deleteReportFilesByIds">
        DELETE FROM edr_report_record_file
        WHERE fileId IN (
            <foreach collection="fileIds" item="fileId" separator=",">
                #{fileId}
            </foreach>
            )
    </delete>

    <update id="updateReportRecordSendStatus">
        update edr_report_record
        set sendStatus = #{sendStatus}
        where id = #{id}
    </update>

    <update id="updateInspectReportSend">
        update aiops_db_report_record
        set reportStatus = #{reportStatus}
        <if test="reportStatus == 3">
            , reportSendTime = #{reportSendTime}
        </if>
        where edrReportRecordId = #{edrReportRecordId}
    </update>

    <select id="getEidByServiceCode" resultType="java.lang.String">
        select eid from supplier_tenant_map where serviceCode = #{serviceCode}
    </select>

    <select id="getReportRecordAutoSet" resultMap="ReportRecordAutoSet">
        select a.id,a.sid,a.serviceCode,a.customerName,a.orgCnNames,a.orgIds,a.reportGenerateType,a.`type`,a.cycle,a.`day`,a.url,a.source,a.status, a.modelVersion, a.processUserId, a.processUserName,
        b.id r_id,b.errasId r_errasId,
        c.id r_rr_id,c.sid r_rr_sid,c.serviceCode r_rr_serviceCode,c.receiverName r_rr_receiverName,c.receiverMail r_rr_receiverMail,c.title r_rr_title,a.sourceId, a.deviceId
        from edr_report_record_auto_set a
        left join edr_report_record_auto_set_receivers b on a.id = b.errasId
        left join report_receiver c on c.id = b.rrId
        where 1=1 and status != -1
        <if test="type!=null and type!=''">
            and a.type= #{type}
        </if>
        <if test="serviceCode!=null and serviceCode!=''">
            and a.serviceCode= #{serviceCode}
        </if>
        <if test="orgIds!=null and orgIds!=''">
            and a.orgIds= #{orgIds}
        </if>
        <if test="modelVersion!=null and modelVersion!=''">
            and a.modelVersion= #{modelVersion}
        </if>
        <if test="sourceId!=null and sourceId!=''">
            and a.sourceId= #{sourceId}
        </if>
        <if test="deviceId!=null and deviceId!=''">
            and a.deviceId= #{deviceId}
        </if>
    </select>

    <select id="getEid" resultType="java.lang.Long">
        select eid from supplier_tenant_map where serviceCode = #{serviceCode}
    </select>

    <select id="getDbReportRecordDetail" resultType="com.digiwin.escloud.aioitms.report.model.db.DbReportRecord">
        select sid, eid, serviceCode, customerName, reportDate, reportType, userId, userName, dataStartDate, dataEndDate,
        deviceIds deviceId, dbType, dbInstanceName, dbServerName, dbId, edrReportRecordId
        from aiops_db_report_record
        where id = #{id}
    </select>
    
    <select id="getDbReportRecordId" resultType="java.lang.Long">
        select id
        from aiops_db_report_record
        where edrReportRecordId = #{edrReportRecordId}
    </select>

    <select id="getDbInstanceIpAndName" resultType="java.util.Map">
        select erras.id,
               erras.sourceId AS sourceId,
               `add`.dbInstanceName AS instanceName,
               `add`.dbIpAddress AS instanceIpAddress,
               `add`.dbServerName AS dbServerName,
               add2.dbDisplayName AS instanceDisplayName
        from edr_report_record_auto_set erras
        left join aiops_device_datasource `add` ON `add`.dbId = erras.sourceId
        left join (
            select dbId, GROUP_CONCAT(DISTINCT dbDisplayName SEPARATOR ';') AS dbDisplayName
            from aiops_device_datasource
            inner join aiops_device ad2 ON adId = ad2.id
            left join supplier_tenant_map stm ON stm.eid = ad2.eid
            where isDelete = 0
            and stm.serviceCode = #{serviceCode}
            group by dbId
        ) add2 ON `add`.dbId = add2.dbId
        where erras.id = #{id} and erras.serviceCode = #{serviceCode}
        group by erras.id
    </select>
</mapper>