package com.digiwin.escloud.aiouser.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.cache.TenantCache;
import com.digiwin.escloud.aiouser.cache.UserCache;
import com.digiwin.escloud.aiouser.constant.AioUserConst;
import com.digiwin.escloud.aiouser.dao.*;
import com.digiwin.escloud.aiouser.model.app.Domain;
import com.digiwin.escloud.aiouser.model.common.Invitation;
import com.digiwin.escloud.aiouser.model.common.InvitationGetResponse;
import com.digiwin.escloud.aiouser.model.common.InviteType;
import com.digiwin.escloud.aiouser.model.omc.OmcOrder;
import com.digiwin.escloud.aiouser.model.org.Department;
import com.digiwin.escloud.aiouser.model.supplier.*;
import com.digiwin.escloud.aiouser.model.tenant.NotExpiredTenant;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContract;
import com.digiwin.escloud.aiouser.model.user.AuthoredUser;
import com.digiwin.escloud.aiouser.model.user.*;
import com.digiwin.escloud.aiouser.service.*;
import com.digiwin.escloud.aiouser.service.iamapp.IAppUserService;
import com.digiwin.escloud.aiouser.service.iamapp.impl.AppService;
import com.digiwin.escloud.aiouser.service.invite.IHandleInvitation;
import com.digiwin.escloud.aiouser.util.*;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioBasicFeignClient;
import com.digiwin.escloud.common.feign.UserV2FeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.tenant.ServiceStaffRequest;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.integration.api.cac.req.CountReq;
import com.digiwin.escloud.integration.api.iam.IamPermissionService;
import com.digiwin.escloud.integration.api.iam.ManagerRoleService;
import com.digiwin.escloud.integration.api.iam.req.org.OrgVO;
import com.digiwin.escloud.integration.api.iam.req.org.QueryUserInOrgResultVO;
import com.digiwin.escloud.integration.api.iam.req.org.UserWithOrgsVO;
import com.digiwin.escloud.integration.api.iam.req.user.*;
import com.digiwin.escloud.integration.api.iam.res.app.SingleAppRes;
import com.digiwin.escloud.integration.api.iam.res.manager.role.RoleInfoRes;
import com.digiwin.escloud.integration.api.iam.res.permission.QueryUserPermissionRes;
import com.digiwin.escloud.integration.api.iam.res.userpairrole.UserRelationRoleRes;
import com.digiwin.escloud.integration.common.WatchRxError;
import com.digiwin.escloud.integration.service.CacService;
import com.digiwin.escloud.integration.service.IamService;
import com.digiwin.escloud.integration.service.iam.FunctionService;
import com.digiwin.escloud.integration.service.iam.PermissionService;
import com.digiwin.escloud.integration.service.iam.RoleService;
import com.digiwin.escloud.integration.service.iam.common.ITMSRoleType;
import com.digiwin.escloud.integration.service.iam.common.QueryPermissionType;
import com.digiwin.escloud.integration.service.iam.common.RoleType;
import com.digiwin.escloud.integration.service.iam.common.RoleTypeCustom;
import com.digiwin.escloud.messagelibrary.Model.MessageDestination;
import com.digiwin.escloud.messagelibrary.ProducerBaseInterface;
import com.digiwin.escloud.userv2.model.DeptStaff;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@Service
@Slf4j
@RefreshScope
public class UserService extends ProducerBaseInterface<Boolean, Invitation> implements IUserService, ParamCheckHelp {
    private static final SnowFlake SNOW_FLAKE = SnowFlake.getInstance();

    //region
    @Value("${digiwin.escloud.dbname}")
    private String escloudbname;
    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Value("${digiwin.user.defaultserviceregion}")
    private String defaultServiceRegion;
    @Value("${digiwin.user.defaulttimezone}")
    private String defaultTimeZone;
    @Value("${digiwin.supplierSid:241199971893824}")
    private long digiwinSupplierSid;
    @Value("${digiwin.user.connectarea}")
    private String connectArea;
    @Value("${digiwin.issue.servertimezone:08:00}")
    private String servertimezone;
    @Value("${digiwin.employeeinvite.linkurl}")
    private String employeeInviteLinkUrl;
    @Value("${digiwin.employeelogin.linkurl}")
    private String employeeLoginLinkUrl;
    @Value("${digiwin.aio.employeeinvite.linkurl}")
    private String aioEmployeeInviteLinkUrl;
    @Value("${digiwin.aio.employeelogin.linkurl}")
    private String aioEmployeeLoginLinkUrl;
    @Value("${digiwin.aio.service.employeelogin.linkurl}")
    private String aioServiceEmployeeLoginLinkUrl;
    @Value("${digiwin.aio.service.employeeinvite.linkurl}")
    private String aioServiceEmployeeInviteLinkUrl;
    @Value("${digiwin.aio.user.register.linkurl}")
    private String userRegisterUrl;
    @Value("${digiwin.visitor.public.user.eid}")
    private Long visitorEid;
    @Value("${digiwin.visitor.public.user.service.code}")
    private String visitorServiceCode;
    @Value("${digiwin.visitor.public.user.id}")
    private String visitorPublicUserId;
    @Value("${digiwin.visitor.public.user.pwd.hash}")
    private String visitorPublicUserPwdHash;
    @Value("${digiwin.visitor.public.login.platform:SCB_APP_A1}")
    private String visitorPublicLoginPlatform;
    @Value("${digiwin.app.code:AIO}")
    private String appCode;
    @Autowired
    private IamService iamService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ISupplierDao supplierDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    IProductDao productDao;
    @Autowired
    private CommonMailService commonMailService;
    @Autowired
    private MessageUtils messageUtils;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private IUserBaseService userBaseService;
    @Autowired
    private IOrgService orgService;
    @Autowired
    private ITpUserDao tpUserDao;
    @Autowired
    private ITenantDao tenantDao;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private AioBasicFeignClient aioBasicFeignClient;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private TenantCache tenantCache;
    @Autowired
    private UserCache userCache;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private CacService cacService;
    @Autowired
    private UserV2FeignClient userV2FeignClient;
    @Resource
    private AppService appService;
    @Autowired
    private ITransferSettingService transferSettingService;
    @Autowired
    private IamUtils iamUtils;
    @Autowired
    RoleService roleService;
    @Autowired
    private ManagerRoleService managerRoleService;
    @Autowired
    private FunctionService functionService;
    @Autowired
    private IamPermissionService iamPermissionService;
    @Resource
    private ServiceMaintenanceService serviceMaintenanceService;
    @Autowired
    private KeyUtils keyUtils;
    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private IOcService ocService;

    @Value("${digiwin.escloud.dbname:escloud-db}")
    private String escloudDBName;
    //endregion

    @Override
    protected Map<MessageDestination, String[]> generateMessage(Invitation invitation, Boolean success) {
        Map<MessageDestination, String[]> messgae = new HashMap<MessageDestination, String[]>();
        if (success) {
            String[] mx = {prepareInviteEmail(invitation)};
            messgae.put(MessageDestination.MAILMESSAGEDESTINATION, mx);
        }
        return messgae;
    }

    @Override
    protected Boolean realBusiness(Invitation ad) {
        return true;
    }

    @Override
    protected void exceptionHandler(Exception e) {

    }

    private int getTimeZoneMin(String utimezone) {
        //将时区转成分钟
        int TimeZoneH = 0;
        int TimeZoneM = 0;
        int userTimeZone = 0;
        if (!StringUtils.isEmpty(utimezone)) { //huly: 修复漏洞/bug utimezone != "" && utimezone != null 改成 !StringUtils.isEmpty(utimezone)
            try {
                String[] list = utimezone.split(":");
                if (list.length > 1) {
                    TimeZoneH = Integer.valueOf(list[0]);
                    TimeZoneM = Integer.valueOf(list[1]);
                    userTimeZone = TimeZoneH * 60 + TimeZoneM;
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
        }
        return userTimeZone;
    }

    public SupplierEmployee getSupplierEmployee(long id) {
        return userDao.getSupplierEmployee(id);
    }

    public String prepareInviteEmail(Invitation invitation) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            //2. 获取客服信息
            SupplierEmployee supplierEmployee = getSupplierEmployee(invitation.getInvitedEmployeeId());
            if (supplierEmployee == null) {
                log.info("supplierEmployee is null");
                return null;
            }
            log.info("In prepareInvitationMail");
            log.info(supplierEmployee.getEmail());
            log.info(supplierEmployee.getTimeZone());

            language = supplierEmployee.getLanguage();
            userTimeZone = getTimeZoneMin(supplierEmployee.getTimeZone());

            String formatMailStr;
            Long headerEid = RequestUtil.getHeaderEid();
            String mailMsg = "";
            Mail mail01 = new Mail();
            if (headerEid == 99990000 && language.equals("zh-CN")) {
                formatMailStr = commonMailService.readMailContent("employeeInvitationV2.html", language);
                mailMsg = String.format(formatMailStr, invitation.getInvitedTenantName(),
                        invitation.getLinkUrl());
                mail01.setMailSourceType(MailSourceType.EmployeeInviteV2);
            } else {
                formatMailStr = commonMailService.readMailContent("employeeInvitation.html", language);
                mailMsg = String.format(formatMailStr, invitation.getInviterName(), invitation.getInvitedTenantName(),
                        invitation.getLinkUrl());
                mail01.setMailSourceType(MailSourceType.EmployeeInvite);
            }
            //组织发送邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(supplierEmployee.getEmail());
            String subject = "";
            Supplier supplier = supplierDao.selectSupplierBySid(supplierEmployee.getSid());
            subject = mailUtils.getInviteMailSubject("InviteToJoinTenantSubject", language, supplier.getName());
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
            mail01.setReceivers(receivers01);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(invitation.getId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            Map<String, Object> processParams = new HashMap<>();
            processParams.put("headerEid", headerEid);
            mail01.setProcessParams(processParams);
            mails.add(mail01);
        } catch (Exception ex) {
            log.error("prepareInviteEmail", ex);
        }
        return SerializeUtil.JsonSerialize(mails);
    }

    public String prepareInviteLoginEmail(Invitation invitation) {
        List<Mail> mails = new ArrayList<Mail>();
        try {
            //获得语言别
            String language = defaultLanguage;
            int userTimeZone = 0; //默认时区
            //2. 获取客服信息
            SupplierEmployee supplierEmployee = getSupplierEmployee(invitation.getInvitedEmployeeId());
            if (supplierEmployee == null) {
                log.info("supplierEmployee is null");
                return null;
            }
            log.info("In prepareInviteLoginEmail");
            log.info(supplierEmployee.getEmail());
            log.info(supplierEmployee.getTimeZone());

            language = supplierEmployee.getLanguage();
            userTimeZone = getTimeZoneMin(supplierEmployee.getTimeZone());

            String formatMailStr = commonMailService.readMailContent("inviteToLogin.html", language);
            String mailMsg = String.format(formatMailStr, invitation.getInviterName(), invitation.getInvitedTenantName(),
                    invitation.getLinkUrl());
            //组织发送邮件
            List<String> receivers01 = new ArrayList<String>();
            receivers01.add(supplierEmployee.getEmail());
            Mail mail01 = new Mail();
            String subject = "";
            Supplier supplier = supplierDao.selectSupplierBySid(supplierEmployee.getSid());
            subject = mailUtils.getInviteMailSubject("InviteToLoginSubject", language, supplier.getName());
            mail01.setSubject(subject);
            mail01.setMessage(mailMsg);
            mail01.setReceivers(receivers01);
            mail01.setMailSourceType(MailSourceType.InviteToLogin);
            mail01.setUrl("");
            mail01.setSourceId(Long.toString(invitation.getId()));
            mail01.setPriority(1);
            mail01.setLanguage(language);
            mails.add(mail01);
        } catch (Exception ex) {
            log.error("prepareInviteEmail", ex);
        }
        return SerializeUtil.JsonSerialize(mails);
    }

    private void sendInviteMail(Invitation invitation, int employeeStatus) {
        try {
            String mailContent;
            if (employeeStatus == EmployeeStatus.UNREGISTERED.getIndex())
                mailContent = prepareInviteEmail(invitation);
            else
                mailContent = prepareInviteLoginEmail(invitation);
            if (StringUtils.isEmpty(mailContent))
                return;
            String[] mx = {mailContent};
            simpleSend(MessageDestination.MAILMESSAGEDESTINATION, mx);
        } catch (Exception ex) {
            log.error("sendInviteMail", ex);
        }
    }

    @Override
    public AuthoredUserGetResponse doLogin(EsLoginUser esLoginUser) {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setErrMsg(ResponseCode.SUCCESS.getMsg());
            String token = RequestUtil.getHeaderToken();
            if (!StringUtils.isEmpty(token)) {
                String tokenUser = stringRedisTemplate.opsForValue().get(token);
                if (StringUtils.isEmpty(tokenUser)) {
                    //如果根据aio redis获取不到用户信息，通过iam解析token获取用户信息
                    IamAuthoredUser iamAuthoredUser = iamService.analyzeToken(token);
                    if (ObjectUtils.isEmpty(iamAuthoredUser)) {
                        res.setCode(ResponseCode.EXPIRED.toString());
                        res.setErrMsg(ResponseCode.EXPIRED.getMsg());
                        return res;
                    }
                    return doLogin(esLoginUser.getAioPlatformEnum(), iamAuthoredUser);
                } else {
                    //根据aio redis获取用户信息
                    AuthoredUser authoredUser = SerializeUtil.JsonDeserialize(stringRedisTemplate.opsForValue().get(token), AuthoredUser.class);
                    if (authoredUser == null) {
                        res.setCode(ResponseCode.EXPIRED.toString());
                        res.setErrMsg(ResponseCode.EXPIRED.getMsg());
                        return res;
                    } else {
                        res.setAuthoredUser(authoredUser);
                        res.setCode(ResponseCode.SUCCESS.toString());
                        res.setErrMsg(ResponseCode.SUCCESS.getMsg());
                        return res;
                    }
                }
            } else {
                //iam登录，判断是否是公司邮箱，如果是走ad验证
                if (esLoginUser.getAioPlatformEnum() == AioPlatformEnum.AIOSSM) {
                    boolean isDigwinUser = esLoginUser.getUserId().endsWith(AioUserConst.DIGIWINMAIL);
                    if (isDigwinUser) {
                        esLoginUser.setIdentityType(IdentityType.service);
                        esLoginUser.setServiceName(AioUserConst.DIGIWIN_SERVICENAME);
                    }
                }
                ResponseBase responseBase = ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).beforeLogin(esLoginUser);
                if (!ResponseCode.SUCCESS.toString().equals(responseBase.getCode())) {
                    return AuthoredUserGetResponse.error(responseBase.getCode(), responseBase.getErrMsg());
                }
                IamAuthoredUser iamAuthoredUser = ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).doLogin(esLoginUser, res);
                ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).afterLogin(esLoginUser, iamAuthoredUser, res);
            }
        } catch (Exception e) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(e.getMessage());
            res.setAuthoredUser(null);
            log.error("doLogin", e);
        }
        return res;
    }

    @Override
    public AuthoredUserGetResponse doLogin(AioPlatformEnum aioPlatformEnum, IamAuthoredUser iamAuthoredUser) {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setErrMsg(ResponseCode.SUCCESS.getMsg());
            EsLoginUser esLoginUser = new EsLoginUser();
            esLoginUser.setAioPlatformEnum(aioPlatformEnum);
            esLoginUser.setSid(RequestUtil.getHeaderSid());
            esLoginUser.setTenantSid(iamAuthoredUser.getTenantSid());
            ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).afterLogin(esLoginUser, iamAuthoredUser, res);
        } catch (Exception e) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(e.getMessage());
            res.setAuthoredUser(null);
            log.error("doLogin after iamLogin", e);
        }
        return res;
    }


    @Override
    public ResponseBase doUpdateUserDefaultSupplier(User user) {
        try {
            return ResponseBase.ok(userDao.updateUserDefaultSupplier(user));
        } catch (Exception e) {
            log.error("doUpdateUserDefaultSupplier fail ", e);
        }
        return ResponseBase.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase doUpdateSupplierContractDevice(SupplierContractDevice supplierContractDevice) {
        try {
            supplierContractDevice.setSupplier_contract_id(AioUserConst.ISV_SUPPLIER_CONTRACT_ID);//ISV客服易聊系统的合约id
            return ResponseBase.ok(userDao.updateSupplierContractDevice(supplierContractDevice));
        } catch (Exception e) {
            log.error("doUpdateSupplierContractDevice fail ", e);
        }
        return ResponseBase.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase getISVAuthorizeCount(long sid) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("sid", sid);
            map.put("supplier_contract_id", AioUserConst.ISV_SUPPLIER_CONTRACT_ID);//ISV客服易聊系统的合约id
            return ResponseBase.ok(userDao.getISVAuthorizeCount(map));
        } catch (Exception e) {
            log.error("getISVAuthorizeCount fail ", e);
        }
        return ResponseBase.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public AuthoredUserGetResponse doTokenLogin() {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        try {
            String authoredUserStr = this.getUserInfoJsonByToken().orElse(null);
            if (StringUtils.isEmpty(authoredUserStr)) {
                res.setCode(ResponseCode.TOKEN_NOT_EXIST.toString());
                res.setErrMsg(ResponseCode.TOKEN_NOT_EXIST.getMsg());
                return res;
            }
            AuthoredUser authoredUser = SerializeUtil.JsonDeserialize(authoredUserStr, AuthoredUser.class);
            res.setAuthoredUser(authoredUser);
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setErrMsg(ResponseCode.SUCCESS.getMsg());
        } catch (Exception e) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(e.getMessage());
            res.setAuthoredUser(null);
            log.error("doTokenLogin", e);
        }
        return res;
    }

    public Optional<String> getUserInfoJsonByToken() {
        String token = RequestUtil.getHeaderToken();
        if (StringUtils.isEmpty(token)) {
            return Optional.empty();
        }
        return Optional.ofNullable(stringRedisTemplate.opsForValue().get(token));
    }

    @Override
    public List<Map<String, String>> getUserRoles(long userSid) {
        List<Map<String, String>> userRoles = iamService.getUserRoles(RequestUtil.getHeaderToken(), null, userSid);
        if (CollectionUtils.isEmpty(userRoles)) {
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            stringRedisTemplate.delete("userauthinfo" + userSid);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            } finally {
                executorService.shutdown();
            }
        }
        return userRoles;
    }

    @Override
    public List<Map<String, String>> getUserSuppliers(String id) {
        return userDao.getUserSuppliers(id);
    }

    @Override
    public InvitationGetResponse getInvitation(long id, Boolean needActivate) {
        InvitationGetResponse res = new InvitationGetResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());

            Invitation invitation = userDao.getInvitation(id);
            res.setInvitation(invitation);
            if (ObjectUtils.isEmpty(invitation)) {
                return res;
            }
            String inviteType = invitation.getInviteType();
            if (StringUtils.isEmpty(inviteType)) {
                return res;
            }
            IHandleInvitation handleInvitation = SpringContextHolder.getBean(inviteType, IHandleInvitation.class);
            if (handleInvitation == null) {
                return res;
            }
            InvitationGetResponse invitationGetResponse = handleInvitation.buildInvitation(res);
            if (ResponseCode.SUCCESS.isSameCode(invitationGetResponse.getCode()) && needActivate) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", id);
                map.put("activated", needActivate);
                userDao.setUserActivated(map);
            }
            return invitationGetResponse;
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("getInvitation", ex);
        }
        return res;
    }

    @Override
    public BaseResponse getInvitationByInviteType(long eid, String inviteType, int pageNum, int pageSize) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("eid", eid);
        map.put("inviteType", inviteType);
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<Invitation> invitationList = userDao.getInvitationByInviteType(map);
        return BaseResponse.ok(new PageInfo<>(invitationList));
    }

    @Override
    public boolean setUserIsNew(long id, Boolean newUser) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("newUser", newUser);
        return userDao.setUserIsNew(map) > 0;
    }

    @Override
    public BaseResponse sendActivateMail(List<Invitation> invitations) {
        List<Long> ids = invitations.stream().map(o -> o.getId()).collect(Collectors.toList());
        userDao.batchUpdateInvitationNoticeTime(ids, LocalDateTime.now());
        for (Invitation invitation : invitations) {
            mailUtils.sendMail(MailSourceType.ActivateUser.toString(), invitation);
        }
        return BaseResponse.ok();
    }

    @Override
    public CheckRes checkEmailExist(String email) {
        return ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).checkEmailExist(email);
    }

    @Override
    public CheckRes checkTelephoneExist(String telephone) {
        return ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).checkTelephoneExist(telephone);
    }

    @Override
    public UserIdCheckRes checkUserIdExist(String userId) {
        return ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).checkUserIdExist(userId);
    }

    @Override
    public EmployeeGetResponse getEmployees(int employeeStatus, UserInOrgQueryParameter userInOrgQueryParameter) {
        EmployeeGetResponse res = new EmployeeGetResponse();
        try {
            long sid = RequestUtil.getHeaderSid();
            long eid = RequestUtil.getHeaderEid();
            String token = RequestUtil.getHeaderToken();
            NotExpiredTenant tenant = tenantDao.getTenantV2(eid);
            long start = (userInOrgQueryParameter.getPageNumber() - 1) * userInOrgQueryParameter.getPageCount();
            HashMap<String, Object> map = new HashMap<>();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(tenant.getSupplierType())) {
                map.put("sid", tenant.getSupplierSid());
                map.put("eid", tenant.getSid());
            } else {
                map.put("sid", sid);
                map.put("eid", eid);
            }

            map.put("status", employeeStatus);
            map.put("content", userInOrgQueryParameter.getContent());
            map.put("orgSid", userInOrgQueryParameter.getOrgSid());
            map.put("orgUri", userInOrgQueryParameter.getOrgUri());
            map.put("orgUriList", userInOrgQueryParameter.getOrgUriList());
            map.put("start", start);
            map.put("count", userInOrgQueryParameter.getPageCount());
//            map.put("eidList", eidList);
            List<SupplierEmployee> employees = userDao.getEmployees(map);
            List<OrgVO> orgAspect = (List<OrgVO>) orgService.getOrgAspect(false);
            if (employeeStatus == EmployeeStatus.REGISTERED.getIndex()) {
                List<String> itmsList = Arrays.stream(ITMSRoleType.values())
                        .map(o -> o.getId())
                        .collect(Collectors.toList());
//                List<String> roleList = Arrays.stream(RoleType.values())
//                        .map(o -> o.getId())
//                        .collect(Collectors.toList());
                Set<String> roleSet = userDao.getRoleByEid(RequestUtil.getHeaderEid(), RequestUtil.getHeaderSid())
                        .stream()
                        .map(PermissionRole::getRoleCode)
                        .collect(Collectors.toSet());
                employees.stream().forEach(o -> {
                    if (!LongUtil.isEmpty(o.getUserSid())) {
                        try {
                            List<Map<String, String>> userRoles = iamService.getUserRoles(token, null, o.getUserSid());
                            List<Map<String, String>> realItmsRoles = userRoles.stream().filter(role -> itmsList.contains(role.get("roleId"))).collect(Collectors.toList());
                            List<Map<String, String>> realRoles = userRoles.stream().filter(role -> roleSet.contains(role.get("roleId"))).collect(Collectors.toList());
                            o.setItmsRoles(realItmsRoles);
                            o.setRoles(realRoles);
                        } catch (Exception e) {
                            log.error("[getEmployees]  userSid error {}", e.getMessage(), e);
                        }
                    }
                    if (!StringUtils.isEmpty(o.getAuthorizedProductCodes())) {
                        List<String> productCodes = new ArrayList<>(Arrays.asList(o.getAuthorizedProductCodes().split(",")));
                        List<SupplierProduct> supplierProducts = productDao.getSupplierProducts(sid, productCodes);
                        o.setAuthorizedProducts(supplierProducts);
                        if (StringUtils.isEmpty(o.getDefaultProductCode())) {
                            o.setDefaultProduct(supplierProducts.size() > 0 ? supplierProducts.get(0) : null);
                            o.setDefaultProductCode(supplierProducts.size() > 0 ? supplierProducts.get(0).getProductCode() : null);
                        } else {
                            supplierProducts.stream()
                                    .filter(e -> o.getDefaultProductCode().equals(e.getProductCode()) && o.getSid() == e.getSid())
                                    .findAny()
                                    .ifPresent(s -> {
                                        o.setDefaultProduct(s);
                                        o.setDefaultProductCode(s.getProductCode());
                                    });
                        }
                    }
                });
            }
            employees.stream().forEach(o -> {
                orgAspect.stream()
                        .filter(org -> o.getOrgSid() == org.getSid())
                        .findFirst()
                        .ifPresent(org -> o.setOrgName(org.getName()));
            });
            //处理部门信息
            handleDepartmentInfo(employees);
            res.setTotalCount(userDao.getEmployeesCount(map));
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setEmployees(employees);
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("getEmployees", ex);
        }
        return res;
    }

    private void handleDepartmentInfo(List<SupplierEmployee> employees) {
        Map<String, String> deptMap = userDao.selectDepartment().stream().collect(Collectors.toMap(
                Department::getDptId, Department::getDptName,
                (oldData, newData) -> oldData));
        //处理部门id对应的名称
        employees.stream().forEach(o -> {
            String deptIds = o.getDeptIds();
            if (!StringUtils.isEmpty(deptIds)) {
                String[] deptIdList = deptIds.split(",");

                List<Map<String, String>> departmentList = Arrays.stream(deptIdList)
                        .map(deptId -> {
                            Map<String, String> departmentMap = new HashMap<>();
                            departmentMap.put("deptId", deptId);
                            departmentMap.put("deptName", deptMap.computeIfAbsent(deptId, k -> ""));
                            return departmentMap;
                        }).collect(Collectors.toList());
                o.setDepartmentList(departmentList);
            }
        });
    }

    @Override
    public SupplierEmployeeInfo getSupplierEmployeeInfo(long sid, long userSid) {
        return userDao.getSupplierEmployeeInfo(sid, userSid);
    }

    @Override
    public UserPersonInfo getUserPersonalInfo(long userSid) {
        return userDao.getUserPersonalInfo(userSid);
    }

    @Override
    public ResponseBase saveEmployee(SupplierEmployee supplierEmployee, String userName, AioPlatformEnum aioPlatformEnum, Long userSid) {
        ResponseBase res = new ResponseBase();
        try {
            long sid = LongUtil.isNotEmpty(RequestUtil.getHeaderServiceProviderSid()) ?
                    RequestUtil.getHeaderServiceProviderSid() : RequestUtil.getHeaderSid();
            long eid = RequestUtil.getHeaderEid();
            String token = RequestUtil.getHeaderToken();

            //据邮箱在iam是否已经存在，如果存在就表示已注册，不存在表示未注册
            HashMap<String, String> emailIamMap = new HashMap<>();
            emailIamMap.put("email", supplierEmployee.getEmail());
            CheckRes checkRes = iamService.checkEmailExist(emailIamMap);
            supplierEmployee.setStatus(checkRes.getIsRegister() ? EmployeeStatus.REGISTERED.getIndex() : EmployeeStatus.UNREGISTERED.getIndex());

            if (StringUtils.isEmpty(supplierEmployee.getLanguage()))
                supplierEmployee.setLanguage(defaultLanguage);
            HashMap<String, Object> map = new HashMap<>();
            map.put("col", "workNo");
            map.put("value", supplierEmployee.getWorkNo());
            map.put("eid", eid);
            HashMap<String, Object> emailMap = new HashMap<>();
            emailMap.put("col", "email");
            emailMap.put("value", supplierEmployee.getEmail());
            emailMap.put("eid", eid);
            HashMap<String, Object> phoneMap = new HashMap<>();
            phoneMap.put("col", "telephone");
            phoneMap.put("value", supplierEmployee.getTelephone());
            phoneMap.put("eid", eid);
            if (supplierEmployee.getId() == 0) {
                map.put("mode", "insert");
                Integer employeeByWorkNo = userDao.getEmployeeByCol(map);
                //服务商运维平台不校验工号
                if (LongUtil.isEmpty(RequestUtil.getHeaderServiceProviderSid()) && employeeByWorkNo != null) {
                    res.setCode(ResponseCode.EXIST_WORKNO.toString());
                    res.setErrMsg(ResponseCode.EXIST_WORKNO.getMsg());
                    return res;
                }
                emailMap.put("mode", "insert");
                Integer employeeByEmail = userDao.getEmployeeByCol(emailMap);
                if (employeeByEmail != null) {
                    res.setCode(ResponseCode.MAIL_DUPLICATED.toString());
                    res.setErrMsg(ResponseCode.MAIL_DUPLICATED.getMsg());
                    return res;
                }
                if (!StringUtils.isEmpty(supplierEmployee.getTelephone())) {
                    phoneMap.put("mode", "insert");
                    Integer employeeByPhone = userDao.getEmployeeByCol(phoneMap);
                    if (employeeByPhone != null) {
                        res.setCode(ResponseCode.PHONE_DUPLICATED.toString());
                        res.setErrMsg(ResponseCode.PHONE_DUPLICATED.getMsg());
                        return res;
                    }
                }
                supplierEmployee.setId(SnowFlake.getInstance().newId());
                supplierEmployee.setSid(sid);
                supplierEmployee.setEid(eid);
                //如果邮箱在iam已经存在，userSid为空就是未激活，未激活可以再次邀请员工加入
                supplierEmployee.setActivated(false);
                userDao.insertEmployee(supplierEmployee);
                inviteEmployee(supplierEmployee, userName, aioPlatformEnum, userSid);
            } else {
                map.put("mode", "update");
                map.put("id", supplierEmployee.getId());
                Integer employeeByWorkNo = userDao.getEmployeeByCol(map);
                if (LongUtil.isEmpty(RequestUtil.getHeaderServiceProviderSid()) && employeeByWorkNo != null) {
                    res.setCode(ResponseCode.EXIST_WORKNO.toString());
                    res.setErrMsg(ResponseCode.EXIST_WORKNO.getMsg());
                    return res;
                }
                emailMap.put("mode", "update");
                emailMap.put("id", supplierEmployee.getId());
                Integer employeeByEmail = userDao.getEmployeeByCol(emailMap);
                if (employeeByEmail != null) {
                    res.setCode(ResponseCode.MAIL_DUPLICATED.toString());
                    res.setErrMsg(ResponseCode.MAIL_DUPLICATED.getMsg());
                    return res;
                }
                if (!StringUtils.isEmpty(supplierEmployee.getTelephone())) {
                    phoneMap.put("mode", "update");
                    phoneMap.put("id", supplierEmployee.getId());
                    Integer employeeByPhone = userDao.getEmployeeByCol(phoneMap);
                    if (employeeByPhone != null) {
                        res.setCode(ResponseCode.PHONE_DUPLICATED.toString());
                        res.setErrMsg(ResponseCode.PHONE_DUPLICATED.getMsg());
                        return res;
                    }
                }
                if (supplierEmployee.getStatus() == EmployeeStatus.REGISTERED.getIndex()) {
                    ExecutorService executorService = Executors.newSingleThreadExecutor();
                    try {
                        executorService.execute(new Runnable() {
                            @Override
                            public void run() {
                                UserWithOrgsVO userWithOrgsVO = new UserWithOrgsVO();
                                userWithOrgsVO.setSid(supplierEmployee.getUserSid());
                                userWithOrgsVO.setUpdateMode(AioUserConst.ORG_ALLUPDATEMODE);
                                List<QueryUserInOrgResultVO> queryUserInOrgResultVOS = new ArrayList<>();
                                QueryUserInOrgResultVO queryUserInOrgResultVO = new QueryUserInOrgResultVO();
                                queryUserInOrgResultVO.setOrgSid(supplierEmployee.getOrgSid());
                                queryUserInOrgResultVOS.add(queryUserInOrgResultVO);
                                userWithOrgsVO.setUserInOrgs(queryUserInOrgResultVOS);
                                iamService.updateUserOrg(token, userWithOrgsVO);
                            }
                        });
                    } finally {
                        executorService.shutdown();
                    }
                } else if (supplierEmployee.getStatus() == EmployeeStatus.UNREGISTERED.getIndex()) {
                }
                userDao.updateEmployee(supplierEmployee);
            }
            if (supplierEmployee.getUserSid() != null && supplierEmployee.getUserSid() > 0) {
                stringRedisTemplate.delete(AioUserConst.USER_ORG_SID + supplierEmployee.getUserSid());
            }
            res.setCode(ResponseCode.SUCCESS.toString());
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("saveEmployee", ex);
        }
        return res;
    }

    private void inviteEmployee(SupplierEmployee supplierEmployee, String userName, AioPlatformEnum aioPlatformEnum, Long userSid) {
        Invitation invitation = new Invitation();
        long invitationId = SnowFlake.getInstance().newId();
        invitation.setId(invitationId);
        invitation.setInviterName(userName);
        invitation.setInvitedEid(RequestUtil.getHeaderEid());
        invitation.setInvitedTenantName(supplierEmployee.getTenantName());
        invitation.setInvitedTenantId(supplierEmployee.getTenantId());
        invitation.setInvitedEmployeeId(supplierEmployee.getId());
        invitation.setInviterUserSid(LongUtil.isNotEmpty(userSid) ? userSid : supplierEmployee.getUserSid());
        invitation.setInvitationDate(new Date());
        invitation.setInvitedSid(RequestUtil.getHeaderSid());
        String linkUrl;
        //邀请注册链接
        if (supplierEmployee.getStatus() == EmployeeStatus.UNREGISTERED.getIndex()) {
            if (aioPlatformEnum == AioPlatformEnum.SSM) {
                linkUrl = employeeInviteLinkUrl + "?invitationId=" + invitationId;
            } else if (aioPlatformEnum == AioPlatformEnum.AIEOM_SERVICE) {
                linkUrl = aioServiceEmployeeInviteLinkUrl + "?invitationId=" + invitationId;
            } else {
                linkUrl = aioEmployeeInviteLinkUrl + "?invitationId=" + invitationId;
            }
        } else {
            if (aioPlatformEnum == AioPlatformEnum.SSM) {
                linkUrl = employeeLoginLinkUrl + "?invitationId=" + invitationId;
            } else if (aioPlatformEnum == AioPlatformEnum.AIEOM_SERVICE) {
                linkUrl = aioServiceEmployeeLoginLinkUrl + "?invitationId=" + invitationId;
            } else {
                linkUrl = aioEmployeeLoginLinkUrl + "?invitationId=" + invitationId;
            }
        }

        invitation.setLinkUrl(linkUrl);
        invitation.setInviteType(InviteType.EMPLOYEE.toString());
        boolean success = userDao.insertInvitation(invitation) > 0;
        String token = RequestUtil.getHeaderToken();
        long eid = RequestUtil.getHeaderEid();
        long sid = RequestUtil.getHeaderSid();
        String appId = RequestUtil.getHeaderFuncAppId();
        if (success) {
            if (supplierEmployee.getStatus() == EmployeeStatus.REGISTERED.getIndex()) {
                UsersIntoTenantParam usersIntoTenantParam = UsersIntoTenantParam.builder()
                        .email(supplierEmployee.getEmail())
                        .token(token)
                        .aioPlatformEnum(aioPlatformEnum == AioPlatformEnum.AIEOM_SERVICE ? AioPlatformEnum.AIEOM_SERVICE : AioPlatformEnum.SSM)
                        .sid(sid)
                        .serviceProviderSid(RequestUtil.getHeaderServiceProviderSid())
                        .tenantSid(eid)
                        .tenantId(supplierEmployee.getTenantId())
                        .build();
                User user = userBaseService.addExistUserIntoTenant(usersIntoTenantParam);
                log.info("user:{}", user);
                Optional.ofNullable(user).ifPresent(o -> {
                    userDao.updateEmployeeActive(supplierEmployee.getId(), o.getSid());
                    ExecutorService executorService = Executors.newSingleThreadExecutor();
                    try {
                        executorService.execute(() -> {
                            List<Map<String, String>> userRoles = iamService.getUserRoles(token, null, o.getSid());
                            if (aioPlatformEnum == AioPlatformEnum.AIEOM_SERVICE) {
                                //
                                permissionService.addUserToRoleRelation(token, o.getId(), Lists.newArrayList(new RoleTypeCustom(RoleType.CustomerService.getId())
                                                , new RoleTypeCustom(RoleType.User.getId()))
                                        , () -> {
                                        });
                            } else {
                                if (CollectionUtils.isEmpty(userRoles)) {
                                    WatchRxError watchRxError = permissionService.adjustUserToRoleRelationV2(
                                            token, o.getId(), RoleType.CustomerService
                                            , () -> {
                                            }
                                    );
                                } else {
                                    boolean matchRole = userRoles.stream()
                                            .map(role -> role.getOrDefault("roleId", ""))
                                            .anyMatch(roleId -> roleId.equals(RoleType.CustomerService.getId()) ||
                                                    roleId.equals(RoleType.CustomerServiceSupervisor.getId()) ||
                                                    roleId.equals(RoleType.SuperManager.getId()));
                                    if (!matchRole) {
                                        WatchRxError watchRxError = permissionService.adjustUserToRoleRelationV2(
                                                token, o.getId(), RoleType.CustomerService
                                                , () -> {
                                                }
                                        );
                                    }
                                }
                            }

                            CountReq countReq = new CountReq();
                            countReq.setCountingId(appId);
                            countReq.setTenantId(supplierEmployee.getTenantId());
                            countReq.setUserId(user.getId());
                            cacService.addUserCount(token, "", countReq);
                        });
                    } catch (Exception ex) {
                        log.error("set role and add cac", ex);
                    } finally {
                        executorService.shutdown();
                    }
                });
            }
            sendInviteMail(invitation, supplierEmployee.getStatus());
        }
    }

    @Override
    public ResponseBase saveManagementDepartment(long id, List<String> deptIdList) {
        String deptIds = null;
        if (!CollectionUtils.isEmpty(deptIdList)) {
            deptIds = deptIdList.stream()
                    .collect(Collectors.joining(","));
        }
        userDao.updateEmployeeDeptIds(id, deptIds);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase inviteRegisterEmployees(SupplierEmployee supplierEmployee, String userName, AioPlatformEnum aioPlatformEnum) {
        ResponseBase res = new ResponseBase();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            inviteEmployee(supplierEmployee, userName, aioPlatformEnum, null);
        } catch (Exception ex) {
            log.error("inviteRegisterEmployees", ex);
            res.setErrMsg(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
        }
        return res;

    }

    @Override
    public AuthoredUserGetResponse registerByInvite(long invitationId, UserBasicInfo userBasicInfo) {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        try {
            //使用导入接口，不使用注册接口，因为注册之后需要更新，最后导入，需要三个调用三个iam的接口，效能肯定慢
//            IamAuthoredUser iamAuthoredUser = iamService.registerUser(registerUser.getRegisterUserVO());
//            iamService.updateUserInfo(registerUser.getUserInfo());

            //注册
            Invitation invitation = userDao.getInvitation(invitationId);
            if (invitation == null) {
                res.setErrMsg(ResponseCode.INVITATION_NOT_EXIST.toString());
                res.setCode(ResponseCode.INVITATION_NOT_EXIST.getMsg());
                return res;
            }
            if (StringUtils.isEmpty(invitation.getInvitedTenantId())) {
                res.setErrMsg(ResponseCode.INVITATED_TENANTID_EMPTY.toString());
                res.setCode(ResponseCode.INVITATED_TENANTID_EMPTY.getMsg());
                return res;
            }
            List<MappingInfo> mappingInfos = new ArrayList<>();
            MappingInfo mappingInfo = new MappingInfo();
            mappingInfo.setTenantId(invitation.getInvitedTenantId());
            mappingInfo.setProviderId(AioUserConst.PROVIDERID);
            mappingInfo.setVerifyUserId(userBasicInfo.getUser().getId());
            mappingInfos.add(mappingInfo);

            List<RoleInfo> roleInfos = new ArrayList<>();
            RoleInfo roleInfo = new RoleInfo();
            roleInfo.setId(AioUserConst.ROLE_CUSTOMERSERVICEID);
            roleInfo.setName(AioUserConst.ROLE_CUSTOMERSERVICENAME);
            roleInfos.add(roleInfo);

            userBasicInfo.getUser().setTenantId(invitation.getInvitedTenantId());
            userBasicInfo.getUser().setTenantName(invitation.getInvitedTenantName());
            userBasicInfo.setMappingInfo(mappingInfos);
            userBasicInfo.setRoleInfo(roleInfos);
            List<UserBasicInfo> userBasicInfos = new ArrayList<>();
            userBasicInfos.add(userBasicInfo);

            log.info("registerByInvite : " + SerializeUtil.JsonSerialize(userBasicInfos));
            iamService.importUserInTenant(userBasicInfos);

            //登录
//            IamAuthoredUser iamAuthoredUser = userBaseService.doBaseLogin(userBasicInfo.getUser().getId(), EncryptionUtil.getSHA256(userBasicInfo.getUser().getPassword()), IdentityType.token);
            IamAuthoredUser iamAuthoredUser = userBaseService.doBaseLoginWithTenant(userBasicInfo.getUser().getTenantId(), userBasicInfo.getUser().getId(), EncryptionUtil.getSHA256(userBasicInfo.getUser().getPassword()), IdentityType.token);
            log.info("iam login : " + SerializeUtil.JsonSerialize(iamAuthoredUser));
            UserPersonalInfo userPersonalInfo = new UserPersonalInfo(iamAuthoredUser.getSid(), defaultLanguage, defaultServiceRegion, defaultTimeZone);
            AuthoredUser authoredUser = userBaseService.userLoginAfterRegister(iamAuthoredUser, invitation.getInvitedSid(), null, userPersonalInfo, invitation.getInvitedEmployeeId());
            log.info("userLoginAfterRegister : " + SerializeUtil.JsonSerialize(authoredUser));

            //加入部门
            SupplierEmployee supplierEmployee = getSupplierEmployee(invitation.getInvitedEmployeeId());
            authoredUser.setOrgSid(supplierEmployee.getOrgSid());
            stringRedisTemplate.opsForValue().set(authoredUser.getToken(), SerializeUtil.JsonSerialize(authoredUser), 1, TimeUnit.DAYS);

            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(new Runnable() {
                    @Override
                    public void run() {
                        UserWithOrgsVO userWithOrgsVO = new UserWithOrgsVO();
                        userWithOrgsVO.setSid(authoredUser.getSid());
                        userWithOrgsVO.setUpdateMode(AioUserConst.ORG_ALLUPDATEMODE);
                        List<QueryUserInOrgResultVO> queryUserInOrgResultVOS = new ArrayList<>();
                        QueryUserInOrgResultVO queryUserInOrgResultVO = new QueryUserInOrgResultVO();
                        queryUserInOrgResultVO.setOrgSid(supplierEmployee.getOrgSid());
                        queryUserInOrgResultVOS.add(queryUserInOrgResultVO);
                        userWithOrgsVO.setUserInOrgs(queryUserInOrgResultVOS);
                        iamService.updateUserOrg(authoredUser.getToken(), userWithOrgsVO);
                    }
                });
            } finally {
                executorService.shutdown();
            }
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setAuthoredUser(authoredUser);
        } catch (Exception ex) {
            log.error("registerByInvite", ex);
            res.setErrMsg(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
        }
        return res;
    }

    @Override
    public EmployeeGetResponse queryEmployeeList(String productCode, long orgSid, long userSid, String content) {
        EmployeeGetResponse res = new EmployeeGetResponse();
        try {
            long sid = RequestUtil.getHeaderSid();
            long eid = RequestUtil.getHeaderEid();

            List<SupplierEmployee> list = userDao.queryEmployeeList(sid, eid, productCode, orgSid, userSid, content);
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setEmployees(list);
            if (!CollectionUtils.isEmpty(list)) {
                res.setTotalCount(list.size());
            }
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            log.error("queryEmployeeList", ex);
        }
        return res;
    }

    @Override
    public boolean logout() {
        Collection<String> keys = new ArrayList<>();
        keys.add("userauthinfo" + RequestUtil.getHeaderUserSid());
        keys.add(RequestUtil.getHeaderToken());
        // 登出得时候 移除这个key 这个key是 用户所属部门
        keys.add(depKey(RequestUtil.getHeaderSid(), RequestUtil.getHeaderEid(), RequestUtil.getHeaderUserSid()));
        return stringRedisTemplate.delete(keys) > 0;
    }

    @Override
    public boolean logoutV2(String token) {
        String authoredUserStr = stringRedisTemplate.opsForValue().get(token);
        if (StringUtils.isEmpty(authoredUserStr)) {
            log.info("token:{} not exist", token);
            return false;
        }

        AuthoredUser authoredUser = JSONObject.parseObject(authoredUserStr, AuthoredUser.class);
        Collection<String> keys = new ArrayList<>();
        keys.add("userauthinfo" + authoredUser.getSid());
        keys.add(token);
        //登出得时候 移除这个key 这个key是 用户所属部门
        keys.add(depKey(RequestUtil.getHeaderSid(), RequestUtil.getHeaderEid(), authoredUser.getSid()));
        return stringRedisTemplate.delete(keys) > 0;
    }


    private String depKey(Long sid, Long eid, Long userSid) {
        return AioUserConst.USER_ORG_SID + sid + ":" + eid + ":" + userSid;
    }

    @Override
    public QueryUserPermissionRes syncFullAuthInfoToRedis() {
//        QueryUserPermissionRes queryResult = new QueryUserPermissionRes();
        // 2.缓存中没有，从IAM获取并存入缓存
        QueryUserPermissionRes queryResult = new QueryUserPermissionRes();
        long sid = RequestUtil.getHeaderUserSid();

        Map<String, String> map = userDao.getUserPairDefaultTenant(sid);

//        String userId = tokenUser.getUserId();map.get("email")
//        String token = tokenUser.getToken();
//        String tenantId = tokenUser.getTenantId();

        String userId = map.get("email");
//        String telephone = map.get("telephone");
        String tenantId = map.get("tenantId");


        QueryUserPermissionRes permissionRes = iamService.getUserRoleAppPermissionRes(userId, tenantId);
//        QueryUserPermissionRes permissionRes = iamService.getUserRoleAppPermissionResV2(sid, userId, token, tenantId);

        BeanUtils.copyProperties(permissionRes, queryResult);
        //if has permission, then save to Redis.
        Optional.ofNullable(queryResult.getResult())
                .ifPresent(permissionResult ->
                        Optional.ofNullable(permissionResult.getPermissions())
                                .ifPresent(row -> {
                                    if (row.size() > 0) {
                                        stringRedisTemplate.opsForValue().set(
                                                "userauthinfo" + RequestUtil.getHeaderUserSid()
                                                , JSONObject.toJSONString(permissionRes)
                                                , 36, TimeUnit.HOURS
                                        );
                                    }
                                })
                );
        return permissionRes;
    }

    @Override
    public VisitorUserResponse getVisitorInfo(Long supplierSid, Long userSid) {
        VisitorUserResponse response = new VisitorUserResponse();
        VisitorUser visitorUser;
        boolean visitorIsExist;
        boolean newVisitor = false;
        LocalDateTime now = DateUtil.getLocalNow();
        String unsignedDateTimeStr = DateUtil.getSomeDateFormatString(now, DateUtil.UNSIGNED_DATE_TIME_FORMATTER); //sdf

        String nickname = "Visitor" + unsignedDateTimeStr;
        //userSid为空，新增访客
        if (LongUtil.isEmpty(userSid)) {
            userSid = SNOW_FLAKE.newId();
            visitorUser = response.getVisitorUser();
            visitorUser.setSid(userSid);
            visitorUser.setSupplierSid(supplierSid);
            visitorUser.setNickname(nickname);
            visitorUser.setCreateTime(new Date());
            visitorIsExist = userDao.insertVisitorUser(visitorUser) > 0;
            response.setData(visitorIsExist);
            newVisitor = true;
        } else {
            //sid不为空，查询访客
            visitorUser = userDao.selectVisitorUserBySid(userSid);
            response.setVisitorUser(visitorUser);
            visitorIsExist = visitorUser != null;
            response.setData(visitorIsExist);
            newVisitor = false;
        }
        //访客存在才取Token
        if (visitorIsExist) {
            //执行登入来取得Token
            EsLoginUser user = new EsLoginUser();
            user.setUserId(this.visitorPublicUserId);

            try {
                //1.客户端生成公私钥
                HashMap<String, String> keyMap = keyUtils.getKeyPairMap();
                if (keyMap != null) {
                    String clientPublicKey = keyMap.get("publicKey");
                    String privateKey = keyMap.get("privateKey");
                    //2.获取服务端公钥
                    String serverPublicKey = keyUtils.getServerPublicky();
                    //3.根据服务端公钥加密客户端公钥
                    String encryptPublicKey = RSAUtils.encryptByPublicKey(clientPublicKey, serverPublicKey);
                    //4.获取加密后的AES的key值
                    String encryptAesKey = keyUtils.getAesPublicky(encryptPublicKey);
                    //5.根据客户端私有解密加密的aes的key值
                    String aesKey = new String(RSAUtils.decryptByPrivateKey(Base64.decodeBase64(encryptAesKey), privateKey));
                    //明文密码
                    String password = new String(EncryptionUtil.decrypt(EncryptionUtil.parseHexStr2Byte(this.visitorPublicUserPwdHash), EncryptionUtil.key.getBytes()));
                    String passwordHash = AESUtils.aesEncryptByBase64(password, aesKey);
                    user.setPasswordHash(passwordHash);
                    user.setClientEncryptPublicKey(encryptPublicKey);
                } else {
                    //明文密码
                    String password = new String(EncryptionUtil.decrypt(EncryptionUtil.parseHexStr2Byte(this.visitorPublicUserPwdHash), EncryptionUtil.key.getBytes()));
                    user.setPasswordHash(EncryptionUtil.getSHA256(password));
                }
            } catch (Exception ex) {
                response.setErrMsg(ex.toString());
                return response;
            }

            user.setIdentityType(IdentityType.token);
            //TODO:透过配置读取登入方式，可能会因为配置定义的枚举不支持，造成异常
            user.setAioPlatformEnum(AioPlatformEnum.valueOf(visitorPublicLoginPlatform));
            AuthoredUserGetResponse res = doLogin(user);
            String successCode = ResponseCode.SUCCESS.toString();
            String code = res.getCode();
            if (successCode.equals(code)) {
                AuthoredUser authoredUser = res.getAuthoredUser();
                visitorUser.setToken(authoredUser.getToken());
                Long realSid = authoredUser.getSid();
                visitorUser.setRealSid(realSid);
                visitorUser.setEid(visitorEid);
                visitorUser.setServiceCode(visitorServiceCode);
                //是新的访客更新真实Sid
                if (newVisitor) {
                    //更新realSid
                    userDao.updateVisitorRealSidBySid(visitorUser.getSid(), realSid);
                }
            }
            response.setCode(code);
            response.setErrMsg(res.getErrMsg());
        }
        return response;
    }

    @Override
    public VisitorUserResponse getVisitorRealUser(Long userSid) {
        VisitorUserResponse response = new VisitorUserResponse();//sid不为空，查询访客
        if (checkParamIsEmpty(response, userSid, "userSid").isPresent()) {
            return response;
        }
        VisitorUser visitorUser = userDao.selectVisitorUserBySid(userSid);
        if (visitorUser == null) {
            ResponseCode code = ResponseCode.VISITOR_NOT_EXIST;
            response.setCode(code.getCode());
            response.setErrMsg(code.getMsg());
            return response;
        }
        visitorUser.setEid(visitorEid);
        visitorUser.setServiceCode(visitorServiceCode);
        visitorUser.setSupplierSid(visitorUser.getSupplierSid());
        response.setVisitorUser(visitorUser);
        response.setCode(ResponseCode.SUCCESS.toString());
        return response;
    }

    @Override
    public void updatePassword(UpdatePasswordByAccountVO updatePasswordByAccountVO) {
        ((IAppUserService) appService.getRealApp(appCode, Domain.USER)).updatePassword(updatePasswordByAccountVO);
    }

    @Override
    public void batchUpdatePassword() {
        List<String> tpUserEmails = userDao.getTpUserEmails();
        tpUserEmails.stream().forEach(o -> {
            UpdatePasswordByAccountVO updatePasswordByAccountVO = new UpdatePasswordByAccountVO();
            updatePasswordByAccountVO.setPassword(AioUserConst.DEFAULT_PWD);
            updatePasswordByAccountVO.setVerificationCode(AioUserConst.DEFAULT_VFCODE);
            updatePasswordByAccountVO.setAccount(o);
            iamService.updatePassword(updatePasswordByAccountVO);
            log.info("batchUpdatePassword id:{}", o);
            try {
                Thread.sleep(500);
            } catch (Exception e) { //huly: 修复漏洞/bug InterruptedException 改成 Exception
                e.printStackTrace();
            }
        });

    }

    @Override
    public ResponseBase updateUserInfo(String email, UserThirdParty userThirdParty) {
        ResponseBase res = new ResponseBase();
        try {
            UserThirdParty tpUser = tpUserDao.getTpUser(userThirdParty);
            User userByEmail = userDao.getUserByEmail(email);
            if (userByEmail != null) {
                String userId = userByEmail.getId();
                List<MappingInfo> mappingInfos = new ArrayList<>();
                MappingInfo mappingInfo = new MappingInfo();
                mappingInfo.setTenantId(tpUser.getTpEid());
                mappingInfo.setProviderId(AioUserConst.PROVIDERID);
                mappingInfo.setVerifyUserId(userId);
                mappingInfos.add(mappingInfo);

                List<RoleInfo> roleInfos = new ArrayList<>();
                RoleInfo roleInfo = new RoleInfo();
                roleInfo.setId(TpUserType.M.equals(userThirdParty.getTpUserType()) ? AioUserConst.ROLE_MISID : AioUserConst.ROLE_ENDUSERID);
                roleInfo.setName(TpUserType.M.equals(userThirdParty.getTpUserType()) ? AioUserConst.ROLE_MISNAME : AioUserConst.ROLE_ENDUSERNAME);
                roleInfos.add(roleInfo);

                UserBasicInfo userBasicInfo = new UserBasicInfo();
                UserImportInfo user = new UserImportInfo();
                user.setId(userId);
                user.setName(userThirdParty.getTpUserName());
                user.setPassword(AioUserConst.DEFAULT_PWD);
                user.setTenantId(tpUser.getTpEid());

                userBasicInfo.setUser(user);
                userBasicInfo.setMappingInfo(mappingInfos);
                userBasicInfo.setRoleInfo(roleInfos);
                List<UserBasicInfo> userBasicInfos = new ArrayList<>();
                userBasicInfos.add(userBasicInfo);
                log.info("updateUserInfo doTpUserRegister : " + SerializeUtil.JsonSerialize(userBasicInfos));
                iamService.importUserInTenant(userBasicInfos);

//            IamAuthoredUser iamAuthoredUser = userBaseService.doBaseLoginWithTenant(userBasicInfo.getUser().getTenantId(), userBasicInfo.getUser().getId(), EncryptionUtil.getSHA256(userBasicInfo.getUser().getPassword()), IdentityType.token);
//            log.info("updateUserInfo am login : " + SerializeUtil.JsonSerialize(iamAuthoredUser));
                //用户修改默认租户
                HashMap<String, Object> map = new HashMap<>();
                map.put("defaultEid", tpUser.getEid());
                map.put("defaultEidSid", tpUser.getSid());
                map.put("name", userThirdParty.getTpUserName());
                map.put("sid", userByEmail.getSid());
                userDao.updateUserDefaultSet(map);

                if (userDao.getUserTenantMap(userByEmail.getSid(), tpUser.getEid()) == null) {
                    UserTenantMap userTenantMap = new UserTenantMap(SnowFlake.getInstance().newId(), userByEmail.getSid(),
                            tpUser.getEid(), tpUser.getSid());
                    userDao.insertUserTenantMap(userTenantMap);
                }

                HashMap<String, Object> tpMap = new HashMap<>();
                tpMap.put("id", tpUser.getId());
                tpMap.put("userId", userByEmail.getId());
                tpUserDao.updateTpUserBanding(tpMap);
            } else {
                if (!StringUtils.isEmpty(email)) {
                    CheckRes checkRes = checkEmailExist(email);
                    if (checkRes.getIsRegister()) {
                        res.setErrMsg(ResponseCode.MAIL_DUPLICATED.toString());
                        res.setCode(ResponseCode.MAIL_DUPLICATED.getMsg());
                        return res;
                    }
                }
                //登录iam
                LoginUser loginUser = new LoginUser();
                loginUser.setUserId(tpUser.getUserId());

                //1.客户端生成公私钥
                HashMap<String, String> keyMap = keyUtils.getKeyPairMap();
                if (keyMap != null) {
                    String clientPublicKey = keyMap.get("publicKey");
                    String privateKey = keyMap.get("privateKey");
                    //2.获取服务端公钥
                    String serverPublicKey = keyUtils.getServerPublicky();
                    //3.根据服务端公钥加密客户端公钥
                    String encryptPublicKey = RSAUtils.encryptByPublicKey(clientPublicKey, serverPublicKey);
                    //4.获取加密后的AES的key值
                    String encryptAesKey = keyUtils.getAesPublicky(encryptPublicKey);
                    //5.根据客户端私有解密加密的aes的key值
                    String aesKey = new String(RSAUtils.decryptByPrivateKey(Base64.decodeBase64(encryptAesKey), privateKey));
                    String passwordHash = AESUtils.aesEncryptByBase64(AioUserConst.DEFAULT_PWD, aesKey);
                    loginUser.setPasswordHash(passwordHash);
                    loginUser.setClientEncryptPublicKey(encryptPublicKey);
                } else {
                    loginUser.setPasswordHash(EncryptionUtil.getSHA256(AioUserConst.DEFAULT_PWD));
                }

                loginUser.setIdentityType(IdentityType.token);
                IamAuthoredUser iamAuthoredUser;
                try {
                    iamAuthoredUser = iamService.doLogin(loginUser);
                } catch (Exception e) {
                    log.error("userId:" + tpUser.getUserId() + " pwd login failed", e);
                    iamAuthoredUser = userBaseService.doBaseLoginWithTenant(tpUser.getTpEid(), tpUser.getUserId());
                }
                //修改iam邮箱
                UserInfo userInfo = new UserInfo();
                userInfo.setId(tpUser.getUserId());
                userInfo.setName(iamAuthoredUser.getUserName());
                userInfo.setEmail(email);
                iamService.updateUserInfo(iamAuthoredUser.getToken(), userInfo);
                //修改本地邮箱
                userDao.updateUserEmail(userInfo.getId(), userInfo.getEmail());
            }
            //修改第三方用户表邮箱
            HashMap<String, Object> tpMap = new HashMap<>();
            tpMap.put("id", tpUser.getId());
            tpMap.put("tpUserEmail", email);
            tpUserDao.updateTpUserEmail(tpMap);
            res.setCode(ResponseCode.SUCCESS.toString());
        } catch (Exception ex) {
            log.error("updateUserInfo", ex);
            res.setErrMsg(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
        }
        return res;
    }

    @Override
    public Object getUserOrg(Boolean needAllOrg) {
        Long orgSid = userCache.getUserOrgSid(RequestUtil.getHeaderUserSid(), RequestUtil.getHeaderSid(), RequestUtil.getHeaderEid());
        if (!needAllOrg) {
            return orgSid;
        }
        List<OrgVO> orgAspect = (List<OrgVO>) orgService.getOrgAspect(false);
        return orgAspect.stream()
                .filter(Objects::nonNull)
                .filter(org -> orgSid == org.getSid())
                .findAny()
                .orElse(new OrgVO());
    }

    @Override
    public User getUserDetail() {
        User user = userDao.selectUser(RequestUtil.getHeaderUserSid());
        if (ObjectUtils.isEmpty(user)) {
            return null;
        }
        HashMap map = restTemplate.getForObject(AioUserConst.WECHAT_TOKEN_URI, HashMap.class);
        String accessToken = map.getOrDefault("accessToken", "").toString();
        if (!StringUtils.isEmpty(accessToken) && !StringUtils.isEmpty(user.getWechat())) {
            WeChatUserInfo weChatUserInfo = restTemplate.getForObject(AioUserConst.WECHAT_USER_URI + "?access_token=" +
                    accessToken + "&openid=" + user.getWechat() + "&lang=zh_CN", WeChatUserInfo.class);
            user.setWechatUserInfo(weChatUserInfo);
        }
        return user;
    }

    @Override
    public User getUserDetailByUserSid(Long userSid) {
        User user = userDao.selectUser(userSid);
        if (ObjectUtils.isEmpty(user)) {
            return null;
        }
        HashMap map = restTemplate.getForObject(AioUserConst.WECHAT_TOKEN_URI, HashMap.class);
        String accessToken = map.getOrDefault("accessToken", "").toString();
        if (!StringUtils.isEmpty(accessToken) && !StringUtils.isEmpty(user.getWechat())) {
            WeChatUserInfo weChatUserInfo = restTemplate.getForObject(AioUserConst.WECHAT_USER_URI + "?access_token=" +
                    accessToken + "&openid=" + user.getWechat() + "&lang=zh_CN", WeChatUserInfo.class);
            user.setWechatUserInfo(weChatUserInfo);
        }
        return user;
    }

    @Override
    public boolean saveUserDetail(User user) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(user.getId());
        userInfo.setName(user.getName());
        userInfo.setEmail(user.getEmail());
        userInfo.setTelephone(user.getTelephone());
        userInfo.setPhone(user.getPhone());
        iamService.updateUserInfo(RequestUtil.getHeaderToken(), userInfo);
        return userDao.updateUser(user) > 0;
    }

    @Override
    public boolean bandingWeChat(String userName, String wechat, boolean banding) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(RequestUtil.getHeaderUserId());
        userInfo.setName(userName);
        if (!banding) {
            wechat = "";
        }
        userInfo.setWechat(wechat);
        iamService.updateUserInfo(RequestUtil.getHeaderToken(), userInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", RequestUtil.getHeaderUserId());
        map.put("wechat", wechat);
        return userDao.updateUserWeChat(map) > 0;
    }

    @Override
    public ResponseBase attentionTenant(long tenantSid, boolean attention, Long userSid, boolean receiveWarningMail,
                                        Long headerEid) {
        if (LongUtil.isEmpty(userSid)) {
            userSid = RequestUtil.getHeaderUserSid();
        }
        if (attention) {
            ResponseBase canAttentionRes = canAttentionTenant(tenantSid, userSid, headerEid);
            if (!ResponseCode.SUCCESS.getCode().equals(canAttentionRes.getCode())) {
                return canAttentionRes;
            }
        }
        // 當取消關注時，將是否接收預警郵件定義為false
        if (!attention) {
            receiveWarningMail = false;
        }
        Long sid = RequestUtil.getHeaderSidOrDefault(digiwinSupplierSid);
        UserTenantAttention userTenantAttention = new UserTenantAttention(SnowFlake.getInstance().newId(),
                userSid, tenantSid, sid, attention, receiveWarningMail);
        try {
            return ResponseBase.ok(userDao.insertUserTenantAttention(userTenantAttention) > 0);
        } catch (Exception ex) {
            log.error("attentionTenant", ex);
            return ResponseBase.error(ex);
        }
    }

    private ResponseBase canAttentionTenant(long eid, Long userSid, Long headerEid) {
        //先查出客服授权产品线
        Long sid = RequestUtil.getHeaderSidOrDefault(digiwinSupplierSid);
        List<String> authorizedProductCodes = tenantService.getAuthorizedProductCodes(userSid, headerEid, sid);
        if (CollectionUtils.isEmpty(authorizedProductCodes)) {
            return ResponseBase.error(ResponseCode.AUTH_PRODUCT_IS_NULL);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        map.put("eid", eid);
        map.put("productCodes", authorizedProductCodes);
        Integer canAttention = userDao.checkUserCanAttention(map);
        return !ObjectUtils.isEmpty(canAttention) ? ResponseBase.ok() : ResponseBase.error(ResponseCode.USER_HAS_NO_ATTENTION_AUTH);
    }

    @Override
    public ResponseBase addTenantAndAttention(String serviceCode, boolean attention, boolean receiveWarningMail,
                                              boolean transferContract) {
        //和慧娟讨论关闭检查，从租户列表操作进行添加租户授权
        //如果要移转合约(表示从外部API请求进来)
        // 先檢查該租戶是否有AIEOM的授權
//        if (ObjectUtils.isEmpty(tenantService.getTenantAppIdInfo(serviceCode, "AIEOM"))) {
//            // 该租户尚未授权应用「企业运维服务云」
//            return ResponseBase.error(ResponseCode.TENANT_NO_APP_AUTH);
//        }
        return addTenantAndAttentionCore(serviceCode, attention, receiveWarningMail, transferContract);
    }

    @Override
    public ResponseBase addTenantAndAttentionCore(String serviceCode, boolean attention,
                                                  boolean receiveWarningMail, boolean transferContract) {
        SupplierTenantMap supplierTenantMap = supplierDao.selectSupplierTenantMapByServiceCode(serviceCode);
        if (!ObjectUtils.isEmpty(supplierTenantMap)) {
            return ResponseBase.error(ResponseCode.EXIST_SERVERCODE);
        }
        ResponseBase res = aioBasicFeignClient.syncTenant(serviceCode);
        if (!ResponseCode.SUCCESS.getCode().equals(res.getCode())) {
            return res;
        }
        //如需移转合约设定，进行移转
        if (transferContract) {
            transferSettingService.transferEsclientContractSetting(false, false,
                    1, Stream.of(serviceCode).collect(Collectors.toList()));
        }
        if (!attention) {
            return res;
        }
        return attentionTenant((long) res.getData(), attention, null, receiveWarningMail, null);
    }

    @Override
    public List<UserTenantAttention> getTenantAndAttention(Long orgSid, Long userSid, Long sid) {
        if (orgSid == null || orgSid == 0) {
            return userDao.getTenantAndAttention(userSid, sid, 1);
        } else {
            return userDao.getTenantAndAttention4Org(orgSid, 1);
        }
    }

    @Override
    public List<User> getAttentionUserByEid(String eid) {
        return userDao.getAttentionUserByEid(eid);
    }

    @Override
    public List<User> getAttentionAndReceiveWarningMailUserByEid(String eid) {
        return userDao.getAttentionAndReceiveWarningMailUserByEid(eid);
    }

    @Override
    public List<UserTenantAttention> getUserTenantAttentions(long userSid, int page, int size) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userSid", userSid);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("attention", true);
        int start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return userDao.getUserTenantAttentions(map);
    }

    @Override
    public int getUserTenantAttentionsCount(long userSid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userSid", userSid);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("attention", true);
        return userDao.getUserTenantAttentionsCount(map);
    }

    @Override
    public ResponseBase attentionTenantReceiveWarningMail(long tenantSid, Long userSid, boolean receiveWarningMail) {
        if (userSid == null || userSid == 0) {
            userSid = RequestUtil.getHeaderUserSid();
        }
        // 檢查是否有關注的權限
        if (receiveWarningMail) {
            ResponseBase canAttentionRes = canAttentionTenant(tenantSid, userSid, null);
            if (!ResponseCode.SUCCESS.getCode().equals(canAttentionRes.getCode())) {
                return canAttentionRes;
            }
        }
        // 檢查是否已關注，已關注才可以繼續
        ResponseBase tenantIsAttentionRes = checkTenantIsAttention(tenantSid, userSid);
        if (!ResponseCode.SUCCESS.getCode().equals(tenantIsAttentionRes.getCode())) {
            return tenantIsAttentionRes;
        }
        //更新receiveWarningMail
        try {
            if (updateAttentionTenantReceiveWarningMail(tenantSid, userSid, receiveWarningMail) > 0) {
                return ResponseBase.ok();
            } else {
                return ResponseBase.error(ResponseCode.USER_HAS_NO_ATTENTION_TENANT);
            }
        } catch (Exception ex) {
            log.error("attentionTenant", ex);
            return ResponseBase.error(ex);
        }

    }

    private ResponseBase checkTenantIsAttention(long eid, Long userSid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", eid);
        map.put("userSid", userSid);
        map.put("attention", true);
        Integer tenantIsAttention = userDao.checkTenantIsAttention(map);
        return !ObjectUtils.isEmpty(tenantIsAttention) ? ResponseBase.ok() : ResponseBase.error(ResponseCode.USER_HAS_NO_ATTENTION_TENANT);
    }

    private int updateAttentionTenantReceiveWarningMail(long eid, Long userSid, boolean receiveWarningMail) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", eid);
        map.put("userSid", userSid);
        map.put("attention", true);
        map.put("receiveWarningMail", receiveWarningMail);
        return userDao.updateAttentionTenantReceiveWarningMail(map);
    }

    @Override
    public ResponseBase updateRolePriority(List<PermissionRole> permissionRoleList) {
        for (PermissionRole role : permissionRoleList) {
            userDao.updateRolePriority(role);
        }
        return ResponseBase.ok();
    }

    @Override
    public List<SupplierEmployee> getEmployeeBasicInfo(String content, Boolean removeAttention, Long tenantSid, Long serviceIsvSid,
                                                       int page, int size) {
        HashMap<String, Object> map = new HashMap<>();
        long sid = LongUtil.isEmpty(serviceIsvSid) ? RequestUtil.getHeaderSidOrDefault(digiwinSupplierSid) : serviceIsvSid;
        map.put("sid", sid);
        map.put("content", content);
        map.put("removeAttention", removeAttention);
        map.put("eid", tenantSid);
        int start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return userDao.getEmployeeBasicInfo(map);
    }

    @Override
    public int getEmployeeBasicInfoCount(String content, Boolean removeAttention, Long tenantSid, Long serviceIsvSid) {
        HashMap<String, Object> map = new HashMap<>();
        long sid = LongUtil.isEmpty(serviceIsvSid) ? RequestUtil.getHeaderSidOrDefault(digiwinSupplierSid) : serviceIsvSid;
        map.put("sid", sid);
        map.put("content", content);
        map.put("removeAttention", removeAttention);
        map.put("eid", tenantSid);
        return userDao.getEmployeeBasicInfoCount(map);
    }

    @Override
    public boolean transferUserTenantAttention(long toUserSid, long[] tenantSids) {
        List<UserTenantAttention> userTenantAttentions = Arrays.stream(tenantSids).mapToObj(o -> new UserTenantAttention(SnowFlake.getInstance().newId(),
                toUserSid, o, RequestUtil.getHeaderSid(), true, false)).collect(Collectors.toList());
        return userDao.batchSaveUserTenantAttention(userTenantAttentions) > 0;
    }

    @Override
    public List<SupplierEmployee> getTenantAttentionUsers(long tenantSid, int page, int size) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", tenantSid);
        int start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return userDao.getTenantAttentionUsers(map);
    }

    @Override
    public int getTenantAttentionUsersCount(long tenantSid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", tenantSid);
        return userDao.getTenantAttentionUsersCount(map);
    }

    @Override
    public List<DeptStaff> getEmployeesNotImported(String[] deptCodes, String content) {
        List<DeptStaff> deptStaffs;
        try {
            ResponseBase deptStaffRes = userV2FeignClient.getDeptStaffList(deptCodes, content);
            if (!ResponseCode.SUCCESS.toString().equals(deptStaffRes.getCode())) {
                return null;
            }
            if (ObjectUtils.isEmpty(deptStaffRes.getData())) {
                return null;
            }

            deptStaffs = JSON.parseObject(JSON.toJSONString(deptStaffRes.getData()), new TypeReference<List<DeptStaff>>() {
            });
            if (CollectionUtils.isEmpty(deptStaffs)) {
                return null;
            }
        } catch (Exception ex) {
            log.error("userV2FeignClient.getDeptStaffList", ex);
            return null;
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        //先查出所有aio员工，如果带上部门可能员工存在老的部门，但是查询出来了
//        map.put("deptCodes", deptCodes);
        map.put("content", content);
        List<SupplierEmployee> employeesByDept = userDao.getEmployeesByDept(map);
        List<String> workNos = employeesByDept.stream().map(o -> o.getWorkNo()).collect(Collectors.toList());
        List<String> emails = employeesByDept.stream().map(o -> o.getEmail()).collect(Collectors.toList());
        return deptStaffs.stream().filter(o -> !workNos.contains(o.getWorkNo()) && !emails.contains(o.getEmail())).collect(Collectors.toList());
    }

    @Override
    public List<SupplierEmployee> importEmployees(List<SupplierEmployee> supplierEmployees, String userName, AioPlatformEnum aioPlatformEnum) {
        List<SupplierEmployee> resSupplierEmployees = new ArrayList<>();
        List<OrgVO> orgAspect = (List<OrgVO>) orgService.getOrgAspect(false);
        supplierEmployees.stream().forEach(o -> {
            boolean orgExisted = orgAspect.stream().anyMatch(org -> o.getOrgLabel().equals(org.getLabel()));
            if (!orgExisted) {
                log.info("import failed name:{},workNo:{},orgLabel:{},res:{}", o.getName(), o.getWorkNo(), o.getOrgLabel(), "org not existed");
                resSupplierEmployees.add(o);
                return;
            }

            ResponseBase res = saveEmployee(o, userName, aioPlatformEnum, null);
            if (!ResponseCode.SUCCESS.toString().equals(res.getCode())) {
                resSupplierEmployees.add(o);
                log.info("import failed name:{},workNo:{},orgLabel:{},res:{}", o.getName(), o.getWorkNo(), o.getOrgLabel(), JSON.toJSONString(res));
            }
        });
        return resSupplierEmployees;
    }

    @Override
    public BaseResponse getUserNameBySidList(List<Long> userSidList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(userSidList, "userSidList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<Map<String, Object>> mapList = userDao.selectUserNameBySidList(userSidList);
        if (CollectionUtils.isEmpty(mapList)) {
            return BaseResponse.ok(new HashMap<>(0));
        }
        return BaseResponse.ok(mapList.stream().filter(x -> x != null)
                .collect(Collectors.toMap(x -> LongUtil.objectToLong(x.get("sid")), x -> x.get("name"), (x, y) -> y)));
    }

    @Override
    public BaseResponse getSeUserSidByWorkNo(String workNo) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(workNo, "workNo");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        Long sid = RequestUtil.getHeaderSidOrDefault(digiwinSupplierSid);

        return BaseResponse.ok(userDao.selectSeUserSidByWorkNo(sid, workNo));
    }

    @Override
    public ResponseBase checkBeforeRegister(CountingUser countingUser) {
        /*//校验角色
        boolean checkRole = iamUtils.checkIsAIEOMDevManager(RequestUtil.getHeaderUserSid(),RequestUtil.getHeaderToken());
        if(!checkRole){
            return ResponseBase.error(ResponseCode.USER_IS_NOT_AIEOMDEVMANAGER);
        }*/
        HashMap<String, String> emailMap = new HashMap<>();
        emailMap.put("email", countingUser.getEmail());
        UserInfo userInfo = iamService.getUserByEmail(RequestUtil.getHeaderToken(), emailMap);
        if (ObjectUtils.isEmpty(userInfo)) {
            if (StringUtils.isEmpty(countingUser.getTelephone())) {
                return ResponseBase.error(ResponseCode.USER_IS_NEW);
            } else {
                HashMap<String, String> phoneMap = new HashMap<>();
                phoneMap.put("telephone", countingUser.getTelephone());
                userInfo = iamService.getUserByTelephone(RequestUtil.getHeaderToken(), phoneMap);
                if (ObjectUtils.isEmpty(userInfo)) {
                    return ResponseBase.error(ResponseCode.USER_IS_NEW);
                }
            }
        }
        User user = userDao.getUserById(userInfo.getId());
        if (!ObjectUtils.isEmpty(user)) {
            UserTenantMap userTenantMap = userDao.getUserTenantMap(user.getSid(), countingUser.getTenantSid());
            if (!ObjectUtils.isEmpty(userTenantMap)) {
                return ResponseBase.error(ResponseCode.USER_IN_AIO_TENANT);
            } else {
                return ResponseBase.error(ResponseCode.USER_NOT_IN_AIO_TENANT);
            }
        } else {
            try {
                ResponseBase res = userV2FeignClient.checkUserInTenant(userInfo.getId(), countingUser.getServiceCode());
                if (ResponseCode.USER_NOT_IN_ES.toString().equals(res.getCode())) {
                    return ResponseBase.error(ResponseCode.USER_IS_NEW);
                }
                return res;
            } catch (Exception e) {
                log.error("userV2FeignClient.checkUserInTenant", e);
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
            }
        }
    }

    //目前僅支援台灣區使用
    public ResponseBase getAllUserFromRoleId(String eid, String roleId) {

        // region 參數檢查
        if (ObjectUtils.isEmpty(eid)) {
            return ResponseBase.error(ResponseCode.PARAM_IS_EMPTY, eid);
        }
        if (ObjectUtils.isEmpty(roleId)) {
            return ResponseBase.error(ResponseCode.PARAM_IS_EMPTY, roleId);
        }
        // endregion

        //根據客代取得租戶資訊，處理不同環境名稱不同問題
        MarsUser marsUser = userDao.getMarsUser(eid, escloudbname)
                .orElseGet(() -> {
                    log.error("Get AllUserFromRoleId Failed: 找不到租戶");
                    throw new RuntimeException("找不到租戶");
                });

        //根據租戶ID、客代、roleId取得指定角色下所有租戶
        List<UserRelationRoleRes> data = roleService.getAllUserFromRoleIdV2(marsUser.getID(), marsUser.getCustomerServiceCode(), roleId);

        return ResponseBase.ok(data);
    }

    @Override
    public void userTenantAttentionBatchImport(MultipartFile file) {

        Workbook workbook = ExcelUtils.readExcel(file);

        //遍历Sheet
        Sheet sheet = workbook.getSheetAt(0);
        //得到每个Sheet的行数,此工作表中包含的最后一行(Row的index是从0开始的)
        int rowCount = sheet.getLastRowNum();
        //遍历Row
        List<UserTenantAttention> userTenantAttentionList = new ArrayList<>();
        for (int j = 1; j <= rowCount; j++) {
            UserTenantAttention userTenantAttention = new UserTenantAttention();

            //得到Row
            Row row = sheet.getRow(j);
            if (row == null) {
                continue;
            }
            //得到每个Row的单元格数
            userTenantAttention.setId(SnowFlake.getInstance().newId());
            userTenantAttention.setUserSid(Long.valueOf(ExcelUtils.getCellFormatValue(row.getCell(1)).toString()));
            userTenantAttention.setEid(Long.valueOf(ExcelUtils.getCellFormatValue(row.getCell(2)).toString()));
            userTenantAttention.setSid(Long.valueOf(ExcelUtils.getCellFormatValue(row.getCell(3)).toString()));
            userTenantAttention.setAttention(!ExcelUtils.getCellFormatValue(row.getCell(4)).equals("0"));
            userTenantAttentionList.add(userTenantAttention);
        }
        if (!CollectionUtils.isEmpty(userTenantAttentionList)) {
            userDao.batchInsertUserTenantAttention(userTenantAttentionList);
        }
    }


    @Override
    public SingleAppRes getFunPermissions() {
        String token = RequestUtil.getHeaderToken();
        return functionService.getAllAppInfo("AIEOM", token);
    }

    @Override
    public ResponseBase getRolePermissionSid(Long sysSid, Long targetSid) {
        String token = RequestUtil.getHeaderToken();
        List<Long> results = new ArrayList<>();
        WatchRxError watchRxError = new WatchRxError();
        iamPermissionService.getRolePermissionSid(token, QueryPermissionType.ROLE.getId(),
                        String.valueOf(sysSid), String.valueOf(targetSid))
                .subscribe(data -> results.addAll(data), watchRxError);
        if (watchRxError.isOccurError()) {
            throw new RuntimeException(watchRxError.getErrorContent());
        }
        return ResponseBase.ok(results);
    }

    @Override
    public List<RoleInfoRes> getAllRoleInTenant() {
        String token = RequestUtil.getHeaderToken();
        List<RoleInfoRes> results = new ArrayList<>();
        WatchRxError watchRxError = new WatchRxError();
        managerRoleService.getAllRoleInTenant(token)
                .subscribe(data -> results.addAll(data), watchRxError);
        if (watchRxError.isOccurError()) {
            throw new RuntimeException(watchRxError.getErrorContent());
        }
        return results;
    }

    @Override
    public ResponseBase batchInsertOrUpdateRole(List<RoleInfoRes> roleInfoResList) {
        long eid = RequestUtil.getHeaderEid();
        long sid = RequestUtil.getHeaderSid();
        userDao.batchInsertOrUpdateRole(eid, sid, roleInfoResList);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase getRole() {
        long eid = RequestUtil.getHeaderEid();
        long sid = RequestUtil.getHeaderSid();
        return ResponseBase.ok(userDao.getRoleByEid(eid, sid));
    }

    @Override
    public ResponseBase<String> getCurrentDept(Long userSid, Long eid, String dynamicValueCode, String userId) {
        if (eid.equals(99990000L)) {
            if ("CurrentUserDepartment".equals(dynamicValueCode)) {
                return handleCurrentUserDepartment(userId);
            } else {
                return handleOtherCases(userId);
            }
        } else {
            return handleNonDefaultEidCase(dynamicValueCode, userId);
        }
    }

    @Override
    public ResponseBase updateSupplierEmpNotActive(Long id) {
        userDao.updateEmployeeNotActive(id);
        return ResponseBase.ok();
    }

    private ResponseBase<String> handleCurrentUserDepartment(String userId) {
        List<String> departmentsList = userDao.selectSelfDepartmentByUserId(userId)
                .stream()
                .map(Department::getDptName)
                .map(dept -> "'" + dept + "%'")
                .collect(Collectors.toList());
        return ResponseBase.okT(String.join(",", departmentsList));
    }

    private ResponseBase<String> handleOtherCases(String userId) {
        Set<String> deptIdsSet = getDeptIdsSet(userId);
        if (CollectionUtils.isEmpty(deptIdsSet)) {
            return ResponseBase.okT("");
        }
        List<Department> deptNameList = userDao.selectManagerDepartmentByDeptIdList(deptIdsSet);
        List<String> departmentsList = deptNameList.stream()
                .map(Department::getDptName)
                .map(dept -> "'" + dept + "%'")
                .collect(Collectors.toList());
        return ResponseBase.okT(String.join(",", departmentsList));
    }

    private Set<String> getDeptIdsSet(String userId) {
        return userDao.selectDeptInfoByUserId(userId)
                .stream()
                .map(SupplierEmployee::getDeptIds)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .flatMap(deptIds -> Arrays.stream(deptIds.split(",")))
                .collect(Collectors.toSet());
    }

    private ResponseBase<String> handleNonDefaultEidCase(String dynamicValueCode, String userId) {
        Set<String> deptIdsSet;
        if ("CurrentUserDepartment".equals(dynamicValueCode)) {
            deptIdsSet = userDao.selectDeptInfoByUserId(userId)
                    .stream()
                    .map(SupplierEmployee::getOrgUri)
                    .map(dept -> "'" + dept + "%'")
                    .collect(Collectors.toSet());
        } else {
            deptIdsSet = getDeptIdsSet(userId)
                    .stream()
                    .map(dept -> "'" + dept + "%'")
                    .collect(Collectors.toSet());
        }
        return ResponseBase.okT(String.join(",", deptIdsSet));
    }

    @Override
    public BaseResponse sendRegisterInvitation(Invitation invitation) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(invitation, "invitation");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        String invitedEmail = invitation.getInvitedEmail();
        optResponse = checkParamIsEmpty(invitedEmail, "invitation.invitedEmail");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        BaseResponse baseResponse = commonUtils.verifyRegisterInfo(null, invitation.getInvitedEmail(), invitation.getInvitedPhone());
        if (!ResponseCode.SUCCESS.isSameCode(baseResponse.getCode())) {
            return baseResponse;
        }
        long invitationId = SnowFlake.getInstance().newId();
        invitation.setId(invitationId);
        invitation.setLinkUrl(userRegisterUrl + "?invitationId=" + invitationId);
        invitation.setInviteType(InviteType.REGISTER.toString());
        invitation.setInvitedSid(RequestUtil.getHeaderSid());
        invitation.setInvitationDate(new Date());
        boolean res = userDao.insertInvitation(invitation) > 0;
        if (res) {
            commonUtils.asyncRun(() -> mailUtils.sendMail("InviteRegister", invitation));
        }
        return BaseResponse.ok(invitationId);
    }

    @Override
    public ModuleStaffSyncMapping getModuleStaff(Long staffId, Long moduleId) {
        return userDao.selectModuleStaffSyncMapping(staffId, moduleId);
    }

    @Override
    public BaseResponse<List<User>> getModuleStaff(ServiceStaffRequest request) {
        List<User> users = userDao.selectModuleStaff(request, escloudDBName);
        if (BooleanUtil.objectToBoolean(request.getNeedDuplicate())) {
            Map<String, User> collect = users.stream().collect(Collectors.toMap(User::getServiceCode, Function.identity(), (o, n) -> o));
            return BaseResponse.okT(new ArrayList<>(collect.values()));
        }
        return BaseResponse.okT(users);
    }

    @Override
    public BaseResponse saveModuleStaff(List<ModuleStaffSyncMapping> staffSyncMappingList) {
        for (ModuleStaffSyncMapping staffSyncMapping : staffSyncMappingList) {
            staffSyncMapping.setId(SnowFlake.getInstance().newId());
            userDao.insertOrUpdateModuleStaffSyncMapping(staffSyncMapping);
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse sendMailAndProcessOther(ProductLine147NoticeMailParams mailParams) {

        Long userSid = mailParams.getUserSid();
        String token = mailParams.getToken();

        Long headerEid = mailParams.getHeaderEid();
        List<ProductLine147NoticeMailParams.TenantModuleInfo> tenantModuleInfoList = mailParams.getTenantModuleInfoList();
        for (ProductLine147NoticeMailParams.TenantModuleInfo tim : tenantModuleInfoList) {
            log.info("[sendMailAndProcessOther] attention eid: {} , userSid: {}", tim.getEid(), userSid);
            if (LongUtil.isNotEmpty(userSid)) {
                attentionTenant(tim.getEid(), true, userSid, false, headerEid);
            }

            List<TenantModuleContract> tmcList = tenantService.getTenantModuleContractsByServiceCode(tim.getServiceCode(), null);
            Date minStartDate = null;
            Date maxEndDate = null;
            TenantModuleContract minStartDateTenantModule = null;
            TenantModuleContract maxEndDateTenantModule = null;
            tmcList = tmcList.stream().filter(Objects::nonNull).filter(x -> x.getId() != null).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(tmcList)) {
                minStartDateTenantModule = tmcList
                        .stream().min(Comparator.comparing(TenantModuleContract::getStartDate))
                        .orElse(null);
                maxEndDateTenantModule = tmcList
                        .stream().max(Comparator.comparing(TenantModuleContract::getEndDate))
                        .orElse(null);
            }
            if (!ObjectUtils.isEmpty(minStartDateTenantModule)) {
                minStartDate = minStartDateTenantModule.getStartDate();
            }
            if (!ObjectUtils.isEmpty(maxEndDateTenantModule)) {
                maxEndDate = maxEndDateTenantModule.getEndDate();
            }

            OmcOrder omcOrder = new OmcOrder();
            omcOrder.setServiceCode(tim.getServiceCode());
            omcOrder.setRemark("alltenant-cn");
            omcOrder.setEffectiveDateTime(cn.hutool.core.date.DateUtil.format(minStartDate, NORM_DATETIME_PATTERN));
            omcOrder.setExpiredDateTime(cn.hutool.core.date.DateUtil.format(maxEndDate, NORM_DATETIME_PATTERN));
            omcOrder.setAppCode("AIEOM");
            omcOrder.setToken(token);

            BaseResponse baseResponse = ocService.preOrder(omcOrder);
            if (!baseResponse.checkIsSuccess()) {
                log.error("[sendMailAndProcessOther] open aieom error param:{} , res:{}", JSONObject.toJSONString(omcOrder), JSONObject.toJSONString(baseResponse));
            }
        }

        log.info("[sendMailAndProcessOther] send mail : {}", JSONObject.toJSONString(mailParams));
        if (LongUtil.isNotEmpty(userSid)) {
            mailUtils.sendMail("ProductLine147NoticeMail", mailParams);
        }
        return BaseResponse.ok();
    }
}
