package com.digiwin.escloud.aiouser.model.tenant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@ApiModel
@Data
public class TenantContract {
    @ApiModelProperty("主键")
    private long id;
    @ApiModelProperty("租户Id")
    private long eid;
    @ApiModelProperty("sid")
    private long sid;
    @ApiModelProperty("产品别")
    private String productCode;
    @ApiModelProperty("产品别短名称")
    private String productShortName;
    @ApiModelProperty("产品版本")
    private String productVersion;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("服务类型")
    private Integer serviceType;
    @ApiModelProperty("文字客服是否走专属")
    private Boolean hasOwnerService;
    @ApiModelProperty("文字客服是否走团队")
    private Boolean hasTextService;
    @ApiModelProperty("案件是否走专属")
    private Boolean hasOwnerIssueService;
    @ApiModelProperty("服务员工Id")
    private Long serviceStaffId;
    @ApiModelProperty("服务员工姓名")
    private String serviceStaffName;
    @ApiModelProperty("合约状态")
    private String contractState;
    @ApiModelProperty("合约状态名称")
    private String contractStateDesc;
    @ApiModelProperty("合约起始日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;
    @ApiModelProperty("合约终止日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractExpiryDate;
    @ApiModelProperty("区域Id")
    private Long areaId;
    @ApiModelProperty("区域Code")
    private String areaCode;
    @ApiModelProperty("行业别Id")
    private Long industryId;
    @ApiModelProperty("行业别Code")
    private String industryCode;
    @ApiModelProperty("行业别名称")
    private String industryName;
    @ApiModelProperty("通路")
    private String access;
    @ApiModelProperty("描述信息(备注)")
    private String description;
    @ApiModelProperty("可客情联系")
    private Boolean canContact;
    @ApiModelProperty("产品名称(简体)")
    private String productShortNameCN;
    @ApiModelProperty("产品名称(繁体)")
    private String productShortNameTW;
    @ApiModelProperty("产品名称(英文)")
    private String productShortNameUS;
    @ApiModelProperty("产品名称(越南文)")
    private String productShortNameVN;
    @ApiModelProperty("产品名称(泰文)")
    private String productShortNameTH;

    private Boolean isTrial;
    @ApiModelProperty("合约是否到期 EXPIRY (到期) NORMAL(正常)")
    private String contractExpiry;


    @ApiModelProperty("移动日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @ApiModelProperty("授權數量")
    private int authorizedNum;

    private String marketUrl;
    private String custLevel;
    private List<Map<String,Object>> serviceRight;
}
