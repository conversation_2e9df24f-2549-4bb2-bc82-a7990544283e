package com.digiwin.escloud.aioitms.exam.model;

import com.digiwin.escloud.aioitms.networksecurity.model.OrganizationalModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class AiopsExamRecordsReportRecord {
    private Long id;
    private Long sid;
    private Long eid;
    private Long aerId;
    private String serviceCode;
    private String customerName;
    private String customerFullName;
    private int reportStatus;
    private Long userSid;
    private String userName;
    private LocalDate reportDate;

    private LocalDateTime generationTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;


    private List<AiopsExamIndexType> indexTypeList;
    @ApiModelProperty("分数")
    private BigDecimal examScore;
    private String levelCode;
    //国产化和组织类模型数据
    private OrganizationalModel organizationalData;
    //预警数据
    private List<Map<String, Object>> warningData;

    private String examTitle;
    private String examEnv;
    private String examReportContent;
    /**
     * 报告类型 目前有 Simple 简式 Complete 完整
     */
    private String reportType;
}
