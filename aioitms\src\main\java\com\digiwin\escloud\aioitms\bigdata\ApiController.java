package com.digiwin.escloud.aioitms.bigdata;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiocmdb.model.ModelRelate;
import com.digiwin.escloud.aiocmdb.model.ModelRelateInstance;
import com.digiwin.escloud.aioitms.additionalinfo.service.AdditionalInfoFactoryService;
import com.digiwin.escloud.aioitms.bigdata.model.*;
import com.digiwin.escloud.aioitms.bigdata.service.IApiService;
import com.digiwin.escloud.aioitms.bigdata.utils.EasyExcelUtil;
import com.digiwin.escloud.aioitms.temprh.service.CallBigDataApiServiceImpl;
import com.digiwin.escloud.aioitms.util.RestUtil;
import com.digiwin.escloud.common.model.DownloadSqlInfo;
import com.digiwin.escloud.common.model.JdbcSqlInfo;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.excel.NoModelWriteData;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.common.model.ResponseCode.SQL_LIMIT_ERROR;
import static com.digiwin.escloud.common.model.ResponseCode.SQL_SYNTAX_ERROR;

/**
 * <AUTHOR>
 * @Date: 2022-11-18 15:29
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/data/api")
public class ApiController {

    @Resource
    private IApiService apiService;
    @Autowired
    private CallBigDataApiServiceImpl callBigDataApiService;
    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    private EasyExcelUtil easyExcelUtil;
    @Autowired
    private AdditionalInfoFactoryService additionalInfoFactoryService;

    @PostMapping("/query/{apiCode}")
    public ResponseBase dataQuery(@PathVariable("apiCode") String apiCode,
                                  @RequestParam(value = "pageNum", required = false, defaultValue = "1") int pageNum,
                                  @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize,
                                  @RequestBody ApiParam apiParam) {
        return ResponseBase.ok(apiService.dataQuery(apiCode, pageNum, pageSize, apiParam));
    }

    @PostMapping("/{apiCode}")
    public ResponseBase bigDataQuery(@PathVariable("apiCode") String apiCode,
                                     @RequestBody Map<String, String> queryParam) {
        CallBigDataApiServiceImpl.QueryResult data = callBigDataApiService.getData(apiCode, queryParam);
        ResponseBase res = new ResponseBase();
        if (ObjectUtils.isEmpty(data)) {
            res.setCode("200");
            res.setErrMsg("success");
            return res;
        }
        res.setData(data.getData());
        //返回数据中台的返回值，方便排查问题
        res.setCode(data.getCode());
        res.setErrMsg(data.getMsg());
        return res;
    }


    @PostMapping("/common/query")
    public ResponseBase commonQuery(@RequestBody() Query query) {
        Map<String, Object> resMap = new HashMap<>();
        if (query.isNeedCnt()) {
            String countSql4Jdbc = QueryWrapperHelper.getCountSql4Jdbc(new QueryWrapper(query));
            List<Map<String, Object>> countDataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), countSql4Jdbc));
            if (CollectionUtils.isEmpty(countDataList)) {
                resMap.put("list", new ArrayList<>());
                resMap.put("total", 0);
                return ResponseBase.ok(resMap);
            }
            resMap = countDataList.get(0);
            Integer total = (Integer) resMap.computeIfAbsent("total", s -> 0);
            if (total == 0) {
                resMap.put("list", new ArrayList<>());
                return ResponseBase.ok(resMap);
            }
        }
        String listSql4Jdbc = QueryWrapperHelper.getSql4Jdbc(new QueryWrapper(query));
        List<Map<String, Object>> dataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), listSql4Jdbc));
        log.info("[commonQuery] query : {}", listSql4Jdbc);
        additionalInfoFactoryService.processByMapList(query.getAdditionalInfoParamList(), dataList);
        if (!query.isNeedCnt()) {
            return ResponseBase.ok(dataList);
        }
        resMap.put("list", dataList);
        return ResponseBase.ok(resMap);
    }

    @PostMapping("/common/query4consultant")
    public ResponseBase commonQuery4Consultant(@RequestBody() Query query) {
        apiService.queryReBuild(query);
        return commonQuery(query);
    }

    @PostMapping("/common/query/by/auth")
    public ResponseBase commonQueryByAuth(@RequestBody() Query query) {
        apiService.queryBuildByAuth(query);
        return commonQuery(query);
    }

    /**
     * 可靠性保证，后期和v1接口合并
     *
     * @param query
     * @return
     */
    @PostMapping("/common/query/v2")
    public ResponseBase commonQueryV2(@RequestBody QueryV2 query) {
        query.setOrder();
        Map<String, Object> resMap = new HashMap<>();
        if (query.isNeedCnt()) {
            String countSql4Jdbc = QueryWrapperHelper.getCountSql4Jdbc(new QueryWrapper(query));
            log.info("Query sql : {}", countSql4Jdbc);
            List<Map<String, Object>> countDataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), countSql4Jdbc));
            if (CollectionUtils.isEmpty(countDataList)) {
                resMap.put("list", new ArrayList<>());
                resMap.put("total", 0);
                return ResponseBase.ok(resMap);
            }
            resMap = countDataList.get(0);
            Integer total = (Integer) resMap.computeIfAbsent("total", s -> 0);
            if (total == 0) {
                resMap.put("list", new ArrayList<>());
                return ResponseBase.ok(resMap);
            }
        }
        String listSql4Jdbc = QueryWrapperHelper.getSql4Jdbc(new QueryWrapper(query));
        log.info("Query sql : {}", listSql4Jdbc);
        List<Map<String, Object>> dataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), listSql4Jdbc));
        if (!query.isNeedCnt()) {
            return ResponseBase.ok(dataList);
        }
        resMap.put("list", dataList);
        return ResponseBase.ok(resMap);
    }

    @PostMapping("/verify/sql")
    public ResponseBase verifySql(@RequestBody JdbcSqlInfo sqlInfo) {
        if (!checkSql(sqlInfo.getSql())) {
            return ResponseBase.error(SQL_SYNTAX_ERROR);
        }
        return ResponseBase.ok();
    }


    @PostMapping("/common/query/v3")
    public ResponseBase commonQueryV3(@RequestBody JdbcSqlInfo query) {
        if (!checkSql(query.getSql())) {
            return ResponseBase.error(SQL_SYNTAX_ERROR);
        }
        if (checkLimit(query.getSql())) {
            return ResponseBase.error(SQL_LIMIT_ERROR);
        }

        List<Map<String, Object>> countDataList = bigDataUtil.jdbcQuery(query);
        return ResponseBase.ok(countDataList);
    }


    @PostMapping("/common/save")
    public ResponseBase commonSave(@RequestParam(required = false, defaultValue = "false") boolean autoId, @RequestBody Map<String, Object> paramMap) {
        String tableName = paramMap.getOrDefault("tableName", "").toString();
        paramMap.remove("tableName");
        if (StringUtils.isBlank(tableName)) {
            return ResponseBase.error(new RuntimeException("tableName is null"));
        }
        if (autoId) {
            if (!paramMap.containsKey("id")) {
                paramMap.put("id", SnowFlake.getInstance().newId());
            }
        }
        List<Object> values = new ArrayList<>();
        List<String> keySet = new ArrayList<>();
        Iterator<Map.Entry<String, Object>> iterator = paramMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> next = iterator.next();
            String key = next.getKey();
            Object valObj = next.getValue();
            if (valObj == null) {
                iterator.remove();
            } else {
                keySet.add(key);
                if (valObj instanceof String) {
                    values.add("'" + valObj + "'");
                } else {
                    values.add(valObj);
                }
            }
        }
        String sql = "insert into " + tableName + "(" +
                StringUtils.join(keySet, ",") + ") values(" + StringUtils.join(values, ",") + ")";
        JdbcSqlInfo sqlInfo = new JdbcSqlInfo("starrocks", sql);
        boolean execute = bigDataUtil.jdbcExecute(sqlInfo);
        if (execute) {
            return ResponseBase.ok(paramMap);
        }
        return ResponseBase.error(ResponseCode.INTERNAL_ERROR, paramMap);
    }

    @PostMapping("/common/download")
    public void commonDownload(@RequestBody() Query query,
                               HttpServletResponse response) {
        NoModelWriteData noModelWriteData = query.getNoModelWriteData();
        if (ObjectUtils.isEmpty(noModelWriteData)) {
            return;
        }
        String listSql4Jdbc = QueryWrapperHelper.getSql4Jdbc(new QueryWrapper(query));
        List<Map<String, Object>> dataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), listSql4Jdbc));
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        noModelWriteData.setDataList(dataList);
        String fileName = query.getNoModelWriteData().getFileName();
        noModelWriteData.setFileName(fileName + "_" +
                DateUtil.getSomeDateFormatString(LocalDateTime.now(), DateUtil.UNSIGNED_DATE_TIME_FORMATTER));
        easyExcelUtil.noModelWrite(noModelWriteData, response);
    }

    /**
     * 可靠性保证，后期和需要 和 /common/download接口合并
     *
     * @param query
     * @return
     */
    @PostMapping("/common/download/v2")
    public void commonDownloadV2(@RequestBody QueryV2 query,
                                 HttpServletResponse response) {
        query.setOrder();
        NoModelWriteData noModelWriteData = query.getNoModelWriteData();
        if (ObjectUtils.isEmpty(noModelWriteData)) {
            return;
        }
        String listSql4Jdbc = QueryWrapperHelper.getSql4Jdbc(new QueryWrapper(query));
        List<Map<String, Object>> dataList = bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), listSql4Jdbc));
        if (StringUtils.isNotBlank(query.getSchemaName())) {
            apiService.dataMapping(dataList, query.getSinkType(), query.getTableName(), query.getSchemaName());
        }

        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        noModelWriteData.setDataList(dataList);
        String fileName = query.getNoModelWriteData().getFileName();
        noModelWriteData.setFileName(fileName + "_" +
                DateUtil.getSomeDateFormatString(LocalDateTime.now(), DateUtil.UNSIGNED_DATE_TIME_FORMATTER));
        easyExcelUtil.noModelWrite(noModelWriteData, response);
    }

    @PostMapping("/common/download/v3")
    public ResponseBase commonDownloadV3(@RequestBody DownloadSqlInfo query,
                                         HttpServletResponse response) {

        if (!checkSql(query.getSql())) {
            return ResponseBase.error(SQL_SYNTAX_ERROR);
        }
        if (checkLimit(query.getSql())) {
            return ResponseBase.error(SQL_LIMIT_ERROR);
        }
        List<Map<String, Object>> dataList = bigDataUtil.jdbcQuery(query);
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        Set<String> keySet = new LinkedHashSet<>();
        dataList.forEach(m -> keySet.addAll(m.keySet()));
        NoModelWriteData noModelWriteData = new NoModelWriteData();
        noModelWriteData.setHeadMap(keySet.toArray(new String[0]));
        noModelWriteData.setDataStrMap(keySet.toArray(new String[0]));

        noModelWriteData.setDataList(dataList);
        String fileName = parseSql(query.getSql(), "tableName");
        noModelWriteData.setFileName(fileName + "_" +
                DateUtil.getSomeDateFormatString(LocalDateTime.now(), DateUtil.UNSIGNED_DATE_TIME_FORMATTER));
        easyExcelUtil.noModelWrite(noModelWriteData, response);
        return null;
    }

    @PostMapping("/web/log/query")
    public ResponseBase webLogQuery(@RequestBody() WebLogQuery webLogQuery) {
        Optional.ofNullable(QueryWrapperHelper.buildWebLogCondition(webLogQuery.getWebLogCondition()))
                .ifPresent(o -> webLogQuery.getBusinessCondition().addAll(o));
        return commonQuery(webLogQuery);
    }

    @PostMapping("/common/time/agg/query")
    public ResponseBase commonTimeAggQuery(@RequestBody() Query query) {
        String groupSql4Jdbc = QueryWrapperHelper.getTimeGroupSql4Jdbc(new QueryWrapper(query));
        return ResponseBase.ok(bigDataUtil.jdbcQuery(new JdbcSqlInfo(query.getSinkType(), groupSql4Jdbc)));
    }

    @PostMapping("/common/time/agg/query4consultant")
    public ResponseBase commonTimeAggQuery4Consultant(@RequestBody() Query query) {
        //对顾问角色增加数据权限
        apiService.queryReBuild(query);
        return commonTimeAggQuery(query);
    }

    @PostMapping("/web/log/agg/query")
    public ResponseBase webLogAggQuery(@RequestBody() WebLogQuery webLogQuery) {
        List<WebLogQuery.WebLogCondition> webLogConditions = QueryWrapperHelper.buildWebLogCondition(webLogQuery.getWebLogCondition());
        if (CollectionUtils.isEmpty(webLogConditions)) {
            return ResponseBase.ok();
        }
        webLogQuery.setWebLogCondition(webLogConditions);
        webLogQuery.getBusinessCondition().addAll(webLogConditions);
        webLogConditions.stream()
                .filter(o -> o.isTransformTimeAgg())
                .findAny()
                .ifPresent(o -> webLogQuery.setTimeGroup(QueryWrapperHelper.buildTimeGroupByWebLog(o)));
        if (ObjectUtils.isEmpty(webLogQuery.getTimeGroup())) {
            return ResponseBase.ok();
        }
        String groupSql4Jdbc = QueryWrapperHelper.getTimeGroupSql4Jdbc(new QueryWrapper(webLogQuery));
        return ResponseBase.ok(bigDataUtil.jdbcQuery(new JdbcSqlInfo(webLogQuery.getSinkType(), groupSql4Jdbc)));
    }

    @PostMapping("/flume")
    public ResponseBase webLogAggQuery(@RequestBody() List<Map<String, Object>> mapList) {
        return ResponseBase.ok(bigDataUtil.simulateLocalUploadData(mapList));
    }

    @GetMapping("/ip")
    public ResponseBase getIp() {
        return ResponseBase.ok(RequestUtil.getIp());
    }

    @PostMapping("/common/saveV2")
    public ResponseBase commonSaveV2(@RequestBody SaveParams saveParams) {
        return apiService.saveData(saveParams);

    }

    @Autowired
    private RestUtil restUtil;

    @GetMapping("/dcdp/test")
    public ResponseBase testDcdp(@RequestBody Object obj) {
//        BaseResponse etlSinkFields = restUtil.getEtlSinkFields("starrocks", "default", "E10OnlineUsersCollectedV2");
//        log.info(JSON.toJSONString(etlSinkFields));

//        BaseResponse saveMrDetail = restUtil.saveMrDetail(99990000L, "LowCodeDesign", "672955983790656",
//                JSON.toJSONString(JSON.toJSONString(obj)));
//        log.info(JSON.toJSONString(saveMrDetail));

//        BaseResponse getModelJson = restUtil.getModelJson(Stream.of("E10OnlineUsersCollectedV2").collect(Collectors.toList()));
//        log.info(JSON.toJSONString(getModelJson));

//        BaseResponse e10OnlineUsersCollectedV2 = restUtil.getAllModel("TransformElement");
//        log.info(JSON.toJSONString(e10OnlineUsersCollectedV2));

//        Map<String, Object> curMap = new HashMap<>();
//        curMap.put("id", 680764087906880L);
//        curMap.put("modelCode", "LowCodeDesign");
//        curMap.put("eid", 99990000L);
//        BaseResponse<List<Map<String, Object>>> mrDetailByMapList = restUtil.getMrDetailByMapList(99990000L, Stream.of(curMap).collect(Collectors.toList()));
//        log.info(JSON.toJSONString(mrDetailByMapList));

//        BaseResponse lowCodeDesign = restUtil.removeMrDetailByIdList(99990000L, "LowCodeDesign", Stream.of("672955983790656").collect(Collectors.toList()));
//        log.info(JSON.toJSONString(lowCodeDesign));

//        Map<String, Object> conditionMap = new HashMap<>();
//        conditionMap.put("sinkType", "starrocks");
//        conditionMap.put("sinkEnable", true);
//        conditionMap.put("schemaName", "default");
//        conditionMap.put("sinkNameList", Stream.of("E10OnlineUsersCollectedV2").collect(Collectors.toList()));
//        BaseResponse<List<EtlEngine>> aieom = restUtil.getEtlListByMap("AIEOM", conditionMap);
//        log.info(JSON.toJSONString(aieom));

//        List<Map<String, Object>> modifyEtlFieldJson = JSON.parseObject("[{\"fieldCode\":\"deviceId\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.deviceId\"},{\"fieldCode\":\"eid\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.eid\"},{\"fieldCode\":\"collectedTime\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.collectedTime\"},{\"fieldCode\":\"collectConfigId\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.collectConfigId\"},{\"fieldCode\":\"uploadDataModelCode\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.uploadDataModelCode\"},{\"fieldCode\":\"deviceCollectDetailId\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.deviceCollectDetailId\"},{\"fieldCode\":\"cpu__time_cpu\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"DataContent.cpu__time_cpu\"},{\"fieldCode\":\"cpu__time_guest\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_guest\"},{\"fieldCode\":\"cpu__time_guest_nice\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_guest_nice\"},{\"fieldCode\":\"cpu__time_idle\",\"fieldType\":\"DECIMAL\",\"valuePath\":\"DataContent.cpu__time_idle\"},{\"fieldCode\":\"cpu__time_iowait\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_iowait\"},{\"fieldCode\":\"cpu__time_irq\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_irq\"},{\"fieldCode\":\"cpu__time_nice\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_nice\"},{\"fieldCode\":\"cpu__time_softirq\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_softirq\"},{\"fieldCode\":\"cpu__time_steal\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__time_steal\"},{\"fieldCode\":\"cpu__time_system\",\"fieldType\":\"DECIMAL\",\"valuePath\":\"DataContent.cpu__time_system\"},{\"fieldCode\":\"cpu__time_user\",\"fieldType\":\"DECIMAL\",\"valuePath\":\"DataContent.cpu__time_user\"},{\"fieldCode\":\"cpu__usage_cpu\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"DataContent.cpu__usage_cpu\"},{\"fieldCode\":\"cpu__usage_guest\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_guest\"},{\"fieldCode\":\"cpu__usage_guest_nice\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_guest_nice\"},{\"fieldCode\":\"cpu__usage_idle\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_idle\"},{\"fieldCode\":\"cpu__usage_iowait\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_iowait\"},{\"fieldCode\":\"cpu__usage_irq\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_irq\"},{\"fieldCode\":\"cpu__usage_nice\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_nice\"},{\"fieldCode\":\"cpu__usage_softirq\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_softirq\"},{\"fieldCode\":\"cpu__usage_steal\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_steal\"},{\"fieldCode\":\"cpu__usage_system\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_system\"},{\"fieldCode\":\"cpu__usage_user\",\"fieldType\":\"INT\",\"valuePath\":\"DataContent.cpu__usage_user\"},{\"fieldCode\":\"timestamp\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"DataContent.timestamp\"}]",
//                new TypeReference<List<Map<String, Object>>>() {
//        });
//        BaseResponse modifyEtlFieldJson = restUtil.modifyEtlFieldJson(14793L, modifyEtlFieldJson);
//        log.info(JSON.toJSONString(modifyEtlFieldJson));

//        ModelRelateInstance modelRelateInstance = JSON.parseObject("{\"sid\":10,\"id\":45,\"modelRelateCode\":\"test_9c78b066b854\",\"relateType\":\"test_048bd1986ab8\",\"sourceModelCode\":\"test_29ceeb704cd9\",\"sourceModelName\":\"test_2955062d84bc\",\"sourceFieldCode\":\"test_06c2129cd02c\",\"sourceFieldValue\":\"test_bc934163f850\",\"targetModelCode\":\"test_7a73a376c08a\"," +
//                "\"targetModelName\":\"test_c5bec01cc562\",\"targetFieldCode\":\"test_7be66f31d6ee\",\"targetFieldValue\":\"test_368d5dfe94c0\"}", ModelRelateInstance.class);
//        BaseResponse saveModelRelateInstanceComplete = restUtil.saveModelRelateInstanceComplete(false, Stream.of(modelRelateInstance).collect(Collectors.toList()));
//        log.info(JSON.toJSONString(saveModelRelateInstanceComplete));

//        BaseResponse batchRemoveModelRelateInstanceByCondition = restUtil.batchRemoveModelRelateInstanceByCondition(Stream.of(modelRelateInstance).collect(Collectors.toList()));
//        log.info(JSON.toJSONString(batchRemoveModelRelateInstanceByCondition));

//        BaseResponse<Map<String, String>> getModelNameMapByCodeList = restUtil.getModelNameMapByCodeList(241199971893824L, Stream.of("E10OnlineUsersCollectedV2").collect(Collectors.toList()));
//        log.info(JSON.toJSONString(getModelNameMapByCodeList));

//        BaseResponse gerMrDetailByKey = restUtil.gerMrDetailByKey("LowCodeDesign", true, Stream.of("99990000", "672955983790656").collect(Collectors.toList()));
//        log.info(JSON.toJSONString(gerMrDetailByKey));

//        ModelRelate modelRelate = JSON.parseObject("{\"sid\":26,\"id\":66,\"modelRelateCode\":\"test_5c1525e8a3f1\",\"relateType\":\"test_7ead5e26df72\"," +
//                "\"sourceModelCode\":\"test_31950d4e8389\",\"sourceModelName\":\"test_5e5f7fec7150\",\"sourceFieldCode\":\"test_a8eca3902f67\",\"targetModelCode\":\"test_aead0ef87638\"," +
//                "\"targetModelName\":\"test_48e34252c4f7\",\"targetFieldCode\":\"test_17f002a11acf\",\"relation\":\"test_2a3221d82224\",\"description\":\"test_b070d2170979\",\"displayMethod\":\"test_37b197975dd5\"}",
//                ModelRelate.class);
//        BaseResponse batchSaveModelRelate = restUtil.batchSaveModelRelate(Stream.of(modelRelate).collect(Collectors.toList()));
//        log.info(JSON.toJSONString(batchSaveModelRelate));
//
//        Map<String, Object> sourceCondition = new HashMap<>();
//        sourceCondition.put("sourceModelCode","HOST");
//        sourceCondition.put("sourceFieldValue","1006");
//        BaseResponse getModelRelateInstanceBySourceInfo = restUtil.getModelRelateInstanceBySourceInfo(Stream.of(sourceCondition).collect(Collectors.toList()));
//        log.info(JSON.toJSONString(getModelRelateInstanceBySourceInfo));

//        BaseResponse removeModelRelateInstance = restUtil.removeModelRelateInstance(Stream.of(45L).collect(Collectors.toList()));
//        log.info(JSON.toJSONString(removeModelRelateInstance));

        return ResponseBase.ok();
    }

    @DeleteMapping("/deleteOracleLinuxDiskioData")
    public ResponseBase deleteOracleLinuxDiskioData(@RequestParam Long eid,@RequestParam String deviceId,@RequestParam String collectedTime) {
        return apiService.deleteOracleLinuxDiskioData(eid, deviceId, collectedTime);
    }

    @GetMapping("/getAsiaVulnInfo")
    public ResponseBase getAsiaVulnInfo(@RequestParam String deviceId,@RequestParam String startTime,@RequestParam String endTime
            ,@RequestParam Long eid) {
        return apiService.getAsiaVulnInfo(deviceId,startTime,endTime,eid);
    }


    private boolean checkLimit(String sql) {
        String limitFind = parseSql(sql, "limit");
        if (StringUtils.isNotBlank(limitFind)) {
            return Integer.parseInt(limitFind) > 8000;
        }
        return false;
    }

    private String parseSql(String sql, String type) {
        // 使用正则表达式匹配FROM关键字后的表名或LIMIT数值
        Pattern pattern;
        Matcher matcher;
        String trimmedSql = sql.trim();

        if ("tableName".equals(type)) {
            pattern = Pattern.compile("(?i)from\\s+(([\\w-]+\\.)?\\w+)");
            matcher = pattern.matcher(trimmedSql);
            if (matcher.find()) {
                String fullTableName = matcher.group(1);
                String[] parts = fullTableName.split("\\.");
                if (parts.length > 1) {
                    return parts[0] + "_" + parts[1];
                } else {
                    return parts[0];
                }
            }
        } else if ("limit".equals(type)) {
            pattern = Pattern.compile("(?i)limit\\s+(\\d+)(?:,\\s*(\\d+))?");
            matcher = pattern.matcher(trimmedSql);
            if (matcher.find()) {
                // 获取 LIMIT 子句中的行数，可以是第一个也可以是第二个数字
                return matcher.group(2) != null ? matcher.group(2) : matcher.group(1);
            }
        }
        return "";
    }

    @GetMapping("/queryDiskDayGrowth")
    public ResponseBase queryDiskDayGrowth(@RequestParam String deviceId) {

        return apiService.queryDiskDayGrowth(deviceId);
    }

    private boolean checkSql(String sql) {
        try {
            CCJSqlParserUtil.parse(sql);
            return true;
        } catch (JSQLParserException e) {
            return false;
        }
    }

    @ApiOperation("查詢資料庫備份失敗詳情")
    @GetMapping("/dbBackupFailedDetail")
    public BaseResponse getDbBackupFailedDetail(@RequestParam String eid,
                                                @RequestParam(required = false) String timeFrom,
                                                @RequestParam(required = false) String timeTo) {
        return apiService.getDbBackupFailedDetail(eid, timeFrom, timeTo);
    }

    @ApiOperation("查詢指定租戶存在預警詳情")
    @GetMapping("/warningDetail")
    public BaseResponse getWarningDetail(@RequestParam String eid,
                                         @RequestParam(required = false) String timeFrom,
                                         @RequestParam(required = false) String timeTo) {
        return apiService.getWarningDetail(eid, timeFrom, timeTo);
    }
}
