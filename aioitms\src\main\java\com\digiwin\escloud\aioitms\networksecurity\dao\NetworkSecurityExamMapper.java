package com.digiwin.escloud.aioitms.networksecurity.dao;


import com.digiwin.escloud.aioitms.exam.model.AiopsExam;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsReportRecord;
import com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamRequest;
import com.digiwin.escloud.aioitms.report.model.db.DbReportRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 体检基础数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Mapper
public interface NetworkSecurityExamMapper {


    // 新增方法：根据code查询exam_id
    Long selectExamIdByCode(@Param("code") String code);

    // 新增方法：分页查询exam_record
    List<AiopsExamRecord> selectExamRecordsByExamId(NetworkSecurityExamRequest request);

    /**
     * 根据 aerId 列表批量查询
     * @param aerIdList aerId 列表
     * @return 查询结果列表
     */
    List<AiopsExamRecordsReportRecord> selectReportByAerIds(@Param("aerIdList") List<Long> aerIdList);


    int insertOrUpdateProjectType(NetworkSecurityExaminationProjectType projectType);

    int deleteByAiopsItemIdAndAerId(@Param("aiopsItemId")String aiopsItemId, @Param("aerId")Long aerId);

    List<NetworkSecurityExaminationProjectType> selectProjectType(@Param("categoryCode") String categoryCode,
                                                                  @Param("categoryName") String categoryName,
                                                                  @Param("parentCode") String parentCode,
                                                                  @Param("filterNullModel") Boolean filterNullModel,
                                                                  @Param("modelCode") String modelCode
    );

    List<AiopsExamItemInstanceScore> selectExamItemInstanceScore(@Param("aerId") Long aerId);

    /**
     * 批量根据code查询examId
     */
    List<AiopsExam> selectExamIdByCodes(@Param("codes") List<String> codes);

    /**
     * 查詢出運維項目數量
     * @param aerId
     * @return
     */
    List<AiopsExamItemInstanceScore> selectExamItemInstanceScoreTotal(@Param("aerId") Long aerId);

    List<AiopsExamRecord> selectExamRecord(@Param("eid") Long eid, @Param("codes") List<String> codes);
    List<DbReportRecord> selectDbReportRecord(@Param("eid") Long eid);

    /**
     * 查询code=100的设备名称
     * @param eid 租户ID
     * @return 设备名称列表
     */
    List<Map<String, Object>> queryDeviceNamesByCode100(@Param("eid") Long eid);

    /**
     * 查询code=08的设备名称
     * @param eid 租户ID
     * @return 设备名称列表
     */
    List<Map<String, Object>> queryDeviceNamesByCode08(@Param("eid") Long eid);
}
