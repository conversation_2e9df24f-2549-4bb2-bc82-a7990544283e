package com.digiwin.escloud.aiouser.service.mail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.aiouser.dao.ISupplierAiopsDao;
import com.digiwin.escloud.aiouser.model.module.ModuleContractDetailBase;
import com.digiwin.escloud.aiouser.model.servicegroup.ServiceGroup;
import com.digiwin.escloud.aiouser.model.supplier.StaffUserInfo;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModule;
import com.digiwin.escloud.aiouser.model.supplier.SupplierAiopsModuleClass;
import com.digiwin.escloud.aiouser.model.tenant.Tenant;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContract;
import com.digiwin.escloud.aiouser.model.tenant.TenantModuleContractDetail;
import com.digiwin.escloud.aiouser.model.user.User;
import com.digiwin.escloud.aiouser.model.usercontact.AuthExpireNoticeGroupRequest;
import com.digiwin.escloud.aiouser.service.ITenantService;
import com.digiwin.escloud.aiouser.service.IUserContactService;
import com.digiwin.escloud.aiouser.util.CommonMailService;
import com.digiwin.escloud.aiouser.util.MailUtils;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.CollectionUtil;
import com.digiwin.escloud.common.util.LongUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public abstract class ProcessMailBase<T extends Tenant> implements ProcessMailHelp {

    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Autowired
    private CommonMailService commonMailService;

    @Autowired
    IUserContactService userContactService;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private ISupplierAiopsDao supplierAiopsDao;

    @Autowired
    private ITenantService tenantService;

    @Value("${service.area}")
    protected String serviceArea;

    protected String[] prepareMailCore(T tenant, String logContent, String mainMailContentHtmlName, String tdHtmlName,
                                       String tableHtmlName, String subjectKey, String subjectTitleKey) {
        return prepareMailCore(tenant, logContent, mainMailContentHtmlName, tdHtmlName, tableHtmlName, subjectKey,
                subjectTitleKey, false, null);
    }

    protected String[] prepareMailCore(T tenant, String logContent, String mainMailContentHtmlName, String tdHtmlName,
                                       String tableHtmlName, String subjectKey, String subjectTitleKey, boolean partMail,
                                       Function<TenantModuleContract, Pair<List<String>, List<String>>> otherReceiverFunc) {
        return prepareMailCore(tenant, logContent, mainMailContentHtmlName, tdHtmlName, tableHtmlName, subjectKey,
                subjectTitleKey, false, this::getMainReceivers, otherReceiverFunc, null);
    }

    protected String[] prepareMailCore(T tenant, String logContent, String mainMailContentHtmlName, String tdHtmlName,
                                       String tableHtmlName, String subjectKey, String subjectTitleKey, boolean partMail,
                                       BiFunction<T, Supplier<Map<String, Object>>, List<String>> mainReceiverFunc,
                                       Function<TenantModuleContract, Pair<List<String>, List<String>>> otherReceiverFunc,
                                       Supplier<Map<String, Object>> supplierOtherInfo) {
        try {
            String language = defaultLanguage;
            List<TenantModuleContract> tmcList = tenant.getTenantModuleContracts();
            if (CollectionUtils.isEmpty(tmcList)) {
                log.info("tenant module contracts is null");
                return null;
            }
            List<String> receivers;
            List<String> ccReceivers;
            if (partMail) {
                receivers = null;
                ccReceivers = null;
            } else {
//                receivers = tmcList.stream()
//                        .map(TenantModuleContract::getNoticeEmail)
//                        .filter(StringUtils::isNotBlank)
//                        .collect(Collectors.toList());
                receivers = mainReceiverFunc.apply(tenant, supplierOtherInfo);

                if (Objects.isNull(otherReceiverFunc)) {
                    ccReceivers = null;
                } else {
                    ccReceivers = new ArrayList<>();
                    tmcList.forEach(x -> {
                        Pair<List<String>, List<String>> otherReceivers = otherReceiverFunc.apply(x);
                        if (Objects.nonNull(otherReceivers)) {
                            List<String> tempReceivers = otherReceivers.getLeft();
                            if (CollectionUtil.isNotEmpty(tempReceivers)) {
                                receivers.addAll(tempReceivers);
                            }
                            tempReceivers = otherReceivers.getRight();
                            if (CollectionUtil.isNotEmpty(tempReceivers)) {
                                ccReceivers.addAll(tempReceivers);
                            }
                        }
                    });
                }
                if (CollectionUtils.isEmpty(receivers)) {
                    log.info(logContent + " email is null");
                    return null;
                }
            }

            String formatMailStr = commonMailService.readMailContent(mainMailContentHtmlName, language);
            StringBuilder sbTables = new StringBuilder();
            tmcList.forEach(tmc -> {
                List<SupplierAiopsModule> supplierAiopsModules =
                        supplierAiopsDao.selectModuleAndClass(tmc.getSid(), null, tmc.getModuleId());
                if (CollectionUtils.isEmpty(supplierAiopsModules)) {
                    return;
                }
                //模组Id为业务主键，因此只取第一笔即可
                SupplierAiopsModule sam = supplierAiopsModules.get(0);
                Map<Long, SupplierAiopsModuleClass> samcIdSamcMap =
                        getSamcIdSamcMap(sam.getSupplierAiopsModuleClassList());
                List<TenantModuleContractDetail> tmcdList = tmc.getTenantModuleContractDetailList();
                List<ModuleContractDetailBase> validTmcdList;
                if (CollectionUtils.isEmpty(tmcdList)) {
                    validTmcdList = null;
                } else {
                    //过滤掉可用数为0的单身纪录
                    validTmcdList = tmcdList.stream().filter(x -> x.getAvailableCount() > 0)
                            .collect(Collectors.toList());
                }
                //处理表格
                String trContent = getTdTableContent(validTmcdList, samcIdSamcMap, language,
                        () -> commonMailService.readMailContent("tdTotalTable.html", language),
                        () -> commonMailService.readMailContent(tdHtmlName, language));
                String mainTemplateContent = commonMailService.readMailContent(tableHtmlName, language);
                sbTables.append(prepareMailTable(mainTemplateContent, language, trContent, tenant, sam, tmc));
            });
            String mailMsg = formatMailStr + "<div style=\"margin: auto;width: 800px\">" + sbTables +
                    "</div>";
            //组织发送邮件
            String tId = tenant.getId();
            String tenantName = tenant.getName();
            //主旨统一补入客户名称
            String subject = "【" + tenantName + "】" + mailUtils.getTrialSubMailSubject(subjectKey, language);
            String subTitle = mailUtils.getTrialSubMailSubject(subjectTitleKey, language);
            if (partMail) {
                return tmcList.stream().map(x -> {
                    List<String> curReceivers = Stream.of(x.getNoticeEmail()).filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    List<String> curCcReceivers;
                    if (Objects.isNull(otherReceiverFunc)) {
                        curCcReceivers = null;
                    } else {
                        Pair<List<String>, List<String>> otherReceivers = otherReceiverFunc.apply(x);
                        if (Objects.isNull(otherReceivers)) {
                            curCcReceivers = null;
                        } else {
                            List<String> tempReceivers = otherReceivers.getLeft();
                            if (CollectionUtil.isNotEmpty(tempReceivers)) {
                                curReceivers.addAll(tempReceivers);
                            }
                            curCcReceivers = otherReceivers.getRight();
                        }
                    }
                    if (CollectionUtils.isEmpty(curReceivers) && CollectionUtils.isEmpty(curCcReceivers)) {
                        log.info("part send email receivers and ccReceivers is empty");
                        return null;
                    }
                    return createMailString(subject, subTitle, mailMsg, curReceivers, curCcReceivers, tId, language);
                }).filter(Objects::nonNull).toArray(String[]::new);
            } else {
                return new String[]{createMailString(subject, subTitle, mailMsg, receivers, ccReceivers,
                        tId, language)};
            }
        } catch (Exception ex) {
            log.error("prepare " + logContent + " mail ", ex);
            return null;
        }
    }

    protected Pair<List<String>, List<String>> getOtherReceivers(TenantModuleContract tmc) {
        if (Objects.isNull(tmc)) {
            return null;
        }
        List<TenantModuleContractDetail> tmcdList = tmc.getTenantModuleContractDetailList();
        Set<Long> samcdIdSet;
        if (CollectionUtils.isEmpty(tmcdList)) {
            samcdIdSet = new HashSet<>(0);
        } else {
            samcdIdSet = tmcdList.stream().map(ModuleContractDetailBase::getSamcId)
                    .filter(LongUtil::isNotEmpty)
                    .collect(Collectors.toSet());
        }

        List<ServiceGroup> sgList;
        if (this.serviceArea.equalsIgnoreCase("tw")) {
            // 台灣區只查 订阅模组, 其它 运维项目, 产品应用 不查
            AuthExpireNoticeGroupRequest request = new AuthExpireNoticeGroupRequest(tmc.getSid(), tmc.getEid(),
                    tmc.getModuleId(), null, null);
            BaseResponse<List<ServiceGroup>> response = userContactService.getAuthExpireNoticeGroupMail(request);

            if (!response.checkIsSuccess() || CollectionUtils.isEmpty(sgList = response.getData())) {
                return null;
            }
            // tw 只處理 163 產品線的
            sgList = sgList.stream()
                    .filter(sg -> "163".equals(sg.getProductCode()))
                    .collect(Collectors.toList());
        } else {
            AuthExpireNoticeGroupRequest request = new AuthExpireNoticeGroupRequest(tmc.getSid(), tmc.getEid(),
                    tmc.getModuleId(), samcdIdSet, tmc.getProductAppCodeMapList());
            BaseResponse<List<ServiceGroup>> response = userContactService.getAuthExpireNoticeGroupMail(request);

            if (!response.checkIsSuccess() || CollectionUtils.isEmpty(sgList = response.getData())) {
                return null;
            }

            // cn 只處理 147 產品線的
            sgList = sgList.stream()
                    .filter(sg -> "147".equals(sg.getProductCode()))
                    .collect(Collectors.toList());
        }

        List<String> receivers = new ArrayList<>();
        List<String> ccReceivers = new ArrayList<>();
        sgList.forEach(x -> {
            String mailCC = x.getMailCC();
            if (StringUtils.isNotBlank(mailCC)) {
                ccReceivers.addAll(Arrays.stream(mailCC.split(";")).filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList()));
            }

            List<StaffUserInfo> groupStaffUserInfos = x.getGroupStaffUserInfos();
            if (CollectionUtil.isNotEmpty(groupStaffUserInfos)) {
                receivers.addAll(groupStaffUserInfos.stream().map(StaffUserInfo::getEmail)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
        });
        return Pair.of(receivers, ccReceivers);
    }

    protected List<String> getMainReceivers(T tenant, Supplier<Map<String, Object>> supplierOtherInfo) {
        List<TenantModuleContract> tmcList = tenant.getTenantModuleContracts();
        return tmcList.stream()
                .map(TenantModuleContract::getNoticeEmail)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    protected List<String> getMisRoleReceivers(T tenant, Supplier<Map<String, Object>> supplierOtherInfo) {
        int page = 1;
        int size = 1000;
        long tenantSid = tenant.getSid();
        String tenantId = tenant.getId();
        Map<String, Object> otherLoginInfo = Optional.ofNullable(supplierOtherInfo.get()).orElse(new HashMap<>());
        List<User> users = tenantService.getTenantUsers(tenantSid, tenantId, "", page, size, otherLoginInfo);
        // 只取 有mis 角色的用戶
        return users.stream()
                .filter(u -> Objects.nonNull(u.getRoles()))
                .filter(u ->
                        u.getRoles().stream()
                                .anyMatch(m ->
                                        m.getOrDefault("roleId", "")
                                                .equalsIgnoreCase("mis")
                                )
                )
                .map(User::getEmail)
                .collect(Collectors.toList());
    }

    private String createMailString(String subject, String subTitle, String mailMsg, List<String> receivers,
                                    List<String> ccReceivers, String sourceId, String language) {
        Mail mail = new Mail();
        mail.setSubject(subject);
        mail.setSubtitle(subTitle);
        mail.setMessage(mailMsg);
        //统一去重复
        if (Objects.nonNull(receivers)) {
            receivers = receivers.stream().distinct().collect(Collectors.toList());
        }
        mail.setReceivers(receivers);
        if (CollectionUtil.isNotEmpty(ccReceivers)) {
            mail.setCcs(ccReceivers.stream().distinct().collect(Collectors.toList()));
        }
        mail.setMailSourceType(MailSourceType.ModuleContractExpire);
        mail.setUrl("");
        mail.setSourceId(sourceId);
        mail.setPriority(1);
        mail.setLanguage(language);
        return JSON.toJSONString(mail);
    }

    protected abstract String prepareMailTable(String mainTemplateContent, String language, String trContent,
                                               T tenant, SupplierAiopsModule sam, TenantModuleContract tmc);
}
