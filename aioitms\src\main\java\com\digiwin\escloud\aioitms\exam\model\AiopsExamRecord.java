package com.digiwin.escloud.aioitms.exam.model;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.digiwin.escloud.aioitms.exam.model.enums.AiopsExamStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 体检记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@Data
@ApiModel(value = "AiopsExamRecord对象", description = "体检记录")
public class AiopsExamRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("体检id")
    private Long aeId;

    private Long sid;

    private Long eid;

    private String serviceCode;

    private String customerName;

    private String customerFullName;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime examStartTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime examEndTime;

    @ApiModelProperty("状态")
    private AiopsExamStatus examStatus;

    @ApiModelProperty("分数")
    private BigDecimal examScore;

    private String userId;
    private String levelCode;

    private String userName;

    @ApiModelProperty("报告类型")
    private String reportType;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    private List<AiopsExamIndexType> indexTypeList;

    private List<AiopsExamItemInstanceScore> aeiisList;
    private List<AiopsItemInstanceData> aiidList;

    private String code;
    private String Content;

    @Data
    public static class AiopsItemInstanceData{
        private String aiopsItem;
        private Long aeimId;
        private List<AiopsExamItemInstanceScore> aeiisList;
    }
    // 产出体检报告使用
    private List<AiopsExamItemScore> aeisList;
    private List<AiopsExamIndexScore> aeIndexList;
    private List<AiopsExamItemTypeScore> aeitsList;
    private List<AiopsExamItemIndexTypeScore> aeiitsList;
    private List<AiopsExamItemIndexScore> aeiIndexScoreList;
    private List<AiopsExamIndexType> aeitList;

    private String examTitle;
    private String examEnv;
    // 产出体检报告使用
    private List<AiopsExamRecordsReportRecord> aerrrList;
    // 只负责展示给前端使用，没有其他用途
    private List<AiopsExamLevelSetting> aelsList;


    @Override
    public String toString() {
        return "AiopsExamRecord{" +
            "id = " + id +
            ", aeId = " + aeId +
            ", sid = " + sid +
            ", eid = " + eid +
            ", serviceCode = " + serviceCode +
            ", customerName = " + customerName +
            ", customerFullName = " + customerFullName +
            ", examStartTime = " + examStartTime +
            ", examEndTime = " + examEndTime +
            ", examStatus = " + examStatus +
            ", examScore = " + examScore +
            ", userId = " + userId +
            ", userName = " + userName +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
