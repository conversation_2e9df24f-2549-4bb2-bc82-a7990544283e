package com.digiwin.escloud.aioitms.networksecurity.model;

import com.digiwin.escloud.aioitms.es.model.EsBase;
import com.digiwin.escloud.aioitms.exam.model.AiopsExamIndexType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.data.elasticsearch.annotations.Document;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ToString
@Data
@Document(indexName="network_security_exam_simple_es_report")
public class NetworkSecurityExamSimpleEsReportRecord extends EsBase {
    private String examReportContent;
    private List<AiopsExamIndexType> indexTypeList;
    private String examTitle;
    private String examEnv;
    private String reportType;
    private String reportDate;

} 